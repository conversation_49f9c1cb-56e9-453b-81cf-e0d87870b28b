# cpu_plot.py
import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics
import matplotlib.dates as mdates

file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\使用工具后没崩溃\YiYuan450_1.0.032.1101.72_20250311_021708\YiYuan450\cpu_mem_log.txt"

time_list, cpu_usage_list = [], []

with open(file_path, 'r', encoding='utf-8') as f:
    for line in f:
        data = json.loads(line)
        time_list.append(datetime.strptime(data['time'], '%Y-%m-%d %H:%M:%S.%f'))
        cpu_usage_list.append(float(data['CpuUsage'].strip('%')))

avg_cpu = statistics.mean(cpu_usage_list)

plt.figure(figsize=(12, 5))
plt.plot(time_list, cpu_usage_list, color='red', marker='o',label='CPU Usage (%)')  # ✅ 无 marker
plt.axhline(avg_cpu, color='red', linestyle='--', alpha=0.6, label=f'Avg CPU: {avg_cpu:.2f}%')

# 时间横坐标优化
ax = plt.gca()
ax.xaxis.set_major_locator(mdates.AutoDateLocator())
ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

plt.xlabel('Time')
plt.ylabel('CPU Usage (%)')
plt.title('CPU Usage Over Time')
plt.xticks(rotation=45)
plt.ylim(0, 100)  # 固定 0-100%
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()
