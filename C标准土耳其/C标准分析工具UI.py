#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C标准分析工具UI
统一的可视化界面，集成CPU、内存和性能分析功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from datetime import datetime
import statistics
import matplotlib.font_manager as fm
import os
import sys
import threading
from io import BytesIO
from PIL import Image

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CStandardAnalysisToolUI:
    def __init__(self, root):
        self.root = root
        self.root.title("C标准分析工具")
        self.root.geometry("1200x800")
        
        # 创建主框架
        self.create_widgets()
        
        # 存储图表对象，用于复制功能
        self.cpu_figure = None
        self.memory_figure = None
        self.performance_figure = None
        
    def create_widgets(self):
        """创建UI组件"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个功能标签页
        self.create_cpu_tab()
        self.create_memory_tab()
        self.create_performance_tab()
        
    def create_cpu_tab(self):
        """创建CPU分析标签页"""
        cpu_frame = ttk.Frame(self.notebook)
        self.notebook.add(cpu_frame, text="CPU分析")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(cpu_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cpu_file_var = tk.StringVar()
        ttk.Label(file_frame, text="CPU数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(file_entry_frame, textvariable=self.cpu_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_cpu_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # 控制按钮
        control_frame = ttk.Frame(cpu_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="分析CPU数据", command=self.analyze_cpu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_cpu_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_cpu_chart).pack(side=tk.LEFT, padx=5)
        
        # 图表区域
        self.cpu_chart_frame = ttk.LabelFrame(cpu_frame, text="CPU使用率图表", padding=10)
        self.cpu_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(cpu_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cpu_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.cpu_result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_memory_tab(self):
        """创建内存分析标签页"""
        memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(memory_frame, text="内存分析")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(memory_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.memory_file_var = tk.StringVar()
        ttk.Label(file_frame, text="内存数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(file_entry_frame, textvariable=self.memory_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_memory_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # 控制按钮
        control_frame = ttk.Frame(memory_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="分析内存数据", command=self.analyze_memory).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_memory_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_memory_chart).pack(side=tk.LEFT, padx=5)
        
        # 图表区域
        self.memory_chart_frame = ttk.LabelFrame(memory_frame, text="内存使用趋势图", padding=10)
        self.memory_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(memory_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.memory_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.memory_result_text.pack(fill=tk.BOTH, expand=True)
        
    def create_performance_tab(self):
        """创建性能分析标签页"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="性能分析")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(performance_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.performance_file_var = tk.StringVar()
        ttk.Label(file_frame, text="性能数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(file_entry_frame, textvariable=self.performance_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_performance_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # 控制按钮
        control_frame = ttk.Frame(performance_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="分析性能数据", command=self.analyze_performance).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_performance_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_performance_chart).pack(side=tk.LEFT, padx=5)
        
        # 图表区域
        self.performance_chart_frame = ttk.LabelFrame(performance_frame, text="性能趋势图", padding=10)
        self.performance_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(performance_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.performance_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.performance_result_text.pack(fill=tk.BOTH, expand=True)
        
    # 文件浏览方法
    def browse_cpu_file(self):
        filename = filedialog.askopenfilename(
            title="选择CPU数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.cpu_file_var.set(filename)
    
    def browse_memory_file(self):
        filename = filedialog.askopenfilename(
            title="选择内存数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.memory_file_var.set(filename)
    
    def browse_performance_file(self):
        filename = filedialog.askopenfilename(
            title="选择性能数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.performance_file_var.set(filename)

    # 图表复制功能
    def copy_figure_to_clipboard(self, figure):
        """将matplotlib图表复制到剪贴板"""
        try:
            # 尝试使用win32clipboard
            try:
                import win32clipboard

                # 将图表保存到内存中的字节流
                buf = BytesIO()
                figure.savefig(buf, format='png', dpi=150, bbox_inches='tight',
                             facecolor='white', edgecolor='none')
                buf.seek(0)

                # 使用PIL打开图像
                img = Image.open(buf)

                # 将图像复制到剪贴板
                output = BytesIO()
                img.convert('RGB').save(output, 'BMP')
                data = output.getvalue()[14:]  # BMP文件头是14字节
                output.close()
                buf.close()

                # 复制到Windows剪贴板
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                win32clipboard.CloseClipboard()

                messagebox.showinfo("成功", "图表已复制到剪贴板！")

            except ImportError:
                # 如果没有win32clipboard，使用PowerShell方法
                self.copy_figure_powershell(figure)

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def copy_figure_powershell(self, figure):
        """使用PowerShell复制图表"""
        try:
            import tempfile
            import subprocess

            # 保存图表到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            figure.savefig(temp_file.name, format='png', dpi=150, bbox_inches='tight',
                         facecolor='white', edgecolor='none')
            temp_file.close()

            # 使用PowerShell复制图片到剪贴板
            cmd = f'powershell.exe "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Clipboard]::SetImage([System.Drawing.Image]::FromFile(\'{temp_file.name}\'))"'
            subprocess.run(cmd, shell=True, check=True)

            # 删除临时文件
            os.unlink(temp_file.name)

            messagebox.showinfo("成功", "图表已复制到剪贴板！")

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def create_chart_context_menu(self, canvas, figure):
        """为图表创建右键菜单"""
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="复制图表", command=lambda: self.copy_figure_to_clipboard(figure))
        context_menu.add_command(label="保存图表", command=lambda: self.save_figure_to_file(figure))

        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        canvas.get_tk_widget().bind("<Button-3>", show_context_menu)  # 右键
        return context_menu

    def save_figure_to_file(self, figure):
        """保存图表到文件"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"C标准图表_{current_time}.png"

            filename = filedialog.asksaveasfilename(
                title="保存图表到文件",
                initialname=default_filename,
                defaultextension=".png",
                filetypes=[
                    ("PNG图片", "*.png"),
                    ("PDF文档", "*.pdf"),
                    ("SVG矢量图", "*.svg"),
                    ("JPEG图片", "*.jpg"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                figure.savefig(filename, dpi=300, bbox_inches='tight',
                             facecolor='white', edgecolor='none')
                messagebox.showinfo("保存成功", f"图表已保存到:\n{filename}")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存图表失败: {str(e)}")

    # CPU分析方法
    def analyze_cpu(self):
        """分析CPU数据"""
        file_path = self.cpu_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择CPU数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_cpu_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_cpu_thread(self, file_path):
        """CPU分析线程"""
        try:
            times = []
            cpu_usages = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if data.get("g_stageStability") == 3:
                            cpu = data.get("current_CPU", 0) * 100  # 转换为百分比
                            time_str = data.get("time", "")
                            if time_str:
                                time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                                times.append(time_obj)
                                cpu_usages.append(cpu)
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的CPU数据"))
                return

            # 计算统计信息
            avg_cpu = statistics.mean(cpu_usages)
            max_cpu = max(cpu_usages)
            min_cpu = min(cpu_usages)

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_cpu_ui(times, cpu_usages, avg_cpu, max_cpu, min_cpu))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_cpu_ui(self, times, cpu_usages, avg_cpu, max_cpu, min_cpu):
        """更新CPU分析UI"""
        # 清除之前的图表
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        ax.plot(times, cpu_usages, label='CPU 使用率 (%)', color='blue', linewidth=1.5)
        ax.axhline(avg_cpu, color='red', linestyle='--', label=f'平均值: {avg_cpu:.2f}%')

        ax.set_xlabel("时间")
        ax.set_ylabel("使用率 (%)")
        ax.set_title("CPU 使用率变化（g_stageStability = 3）")
        ax.legend()
        ax.grid(True, linestyle='--', alpha=0.5)
        ax.set_ylim(0, 100)
        fig.autofmt_xdate()

        # 保存图表对象
        self.cpu_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.cpu_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""CPU分析结果：
数据点数量: {len(cpu_usages)}
平均CPU使用率: {avg_cpu:.2f}%
最大CPU使用率: {max_cpu:.2f}%
最小CPU使用率: {min_cpu:.2f}%
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_result_text.insert(tk.END, result_text)

    def copy_cpu_chart(self):
        """复制CPU图表"""
        if self.cpu_figure:
            self.copy_figure_to_clipboard(self.cpu_figure)
        else:
            messagebox.showwarning("警告", "请先分析CPU数据生成图表")

    def clear_cpu_chart(self):
        """清除CPU图表"""
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()
        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_figure = None

    # 内存分析方法
    def analyze_memory(self):
        """分析内存数据"""
        file_path = self.memory_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择内存数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_memory_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_memory_thread(self, file_path):
        """内存分析线程"""
        try:
            times = []
            memory_values = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        time_str = data.get("time")
                        mem_mb = data.get("current_memory")  # MB单位
                        if time_str and mem_mb is not None:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            memory_values.append(mem_mb)
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的内存数据"))
                return

            # 计算统计信息
            avg_mem = statistics.mean(memory_values)
            max_mem = max(memory_values)
            min_mem = min(memory_values)

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_memory_ui(times, memory_values, avg_mem, max_mem, min_mem))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_memory_ui(self, times, memory_values, avg_mem, max_mem, min_mem):
        """更新内存分析UI"""
        # 清除之前的图表
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        ax.plot(times, memory_values, marker='o', linestyle='-', color='blue', label='内存占用 (MB)')
        ax.axhline(avg_mem, color='orange', linestyle='--', label=f'平均内存占用: {avg_mem:.2f} MB')

        ax.set_xlabel("时间")
        ax.set_ylabel("内存占用 (MB)")
        ax.set_title("内存占用随时间变化")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        # 保存图表对象
        self.memory_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.memory_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""内存分析结果：
数据点数量: {len(memory_values)}
平均内存占用: {avg_mem:.2f} MB
最大内存占用: {max_mem:.2f} MB
最小内存占用: {min_mem:.2f} MB
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.memory_result_text.delete(1.0, tk.END)
        self.memory_result_text.insert(tk.END, result_text)

    def copy_memory_chart(self):
        """复制内存图表"""
        if self.memory_figure:
            self.copy_figure_to_clipboard(self.memory_figure)
        else:
            messagebox.showwarning("警告", "请先分析内存数据生成图表")

    def clear_memory_chart(self):
        """清除内存图表"""
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()
        self.memory_result_text.delete(1.0, tk.END)
        self.memory_figure = None

    # 性能分析方法
    def analyze_performance(self):
        """分析性能数据"""
        file_path = self.performance_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择性能数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_performance_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_performance_thread(self, file_path):
        """性能分析线程"""
        try:
            times = []
            cost_times = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        time_str = data.get("time")
                        cost_time = data.get("costTime")
                        if time_str and cost_time is not None:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            cost_times.append(cost_time)
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的性能数据"))
                return

            # 计算统计信息
            avg_cost_time = statistics.mean(cost_times)
            max_cost_time = max(cost_times)
            min_cost_time = min(cost_times)

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_performance_ui(times, cost_times, avg_cost_time, max_cost_time, min_cost_time))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_performance_ui(self, times, cost_times, avg_cost_time, max_cost_time, min_cost_time):
        """更新性能分析UI"""
        # 清除之前的图表
        for widget in self.performance_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        ax.plot(times, cost_times, marker='o', linestyle='-', color='green', label='处理时间 (costTime, ms)')
        ax.axhline(avg_cost_time, color='orange', linestyle='--', alpha=0.8, label=f'平均处理时间: {avg_cost_time:.2f} ms')

        ax.set_xlabel("时间")
        ax.set_ylabel("处理时间 (毫秒)")
        ax.set_title("costTime 随时间的变化")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        # 保存图表对象
        self.performance_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.performance_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""性能分析结果：
数据点数量: {len(cost_times)}
平均处理时间: {avg_cost_time:.2f} ms
最大处理时间: {max_cost_time:.2f} ms
最小处理时间: {min_cost_time:.2f} ms
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.performance_result_text.delete(1.0, tk.END)
        self.performance_result_text.insert(tk.END, result_text)

    def copy_performance_chart(self):
        """复制性能图表"""
        if self.performance_figure:
            self.copy_figure_to_clipboard(self.performance_figure)
        else:
            messagebox.showwarning("警告", "请先分析性能数据生成图表")

    def clear_performance_chart(self):
        """清除性能图表"""
        for widget in self.performance_chart_frame.winfo_children():
            widget.destroy()
        self.performance_result_text.delete(1.0, tk.END)
        self.performance_figure = None


def main():
    """主程序入口"""
    root = tk.Tk()
    app = CStandardAnalysisToolUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
