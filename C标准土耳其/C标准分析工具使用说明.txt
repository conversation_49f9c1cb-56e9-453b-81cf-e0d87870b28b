===============================================
    C标准分析工具 - 用户使用手册
===============================================

📋 工具简介
-----------
本工具是专为C标准土耳其项目设计的数据分析平台，包含三个主要分析模块：
• CPU使用率分析（g_stageStability = 3）
• 内存使用分析
• 性能耗时分析（costTime）

🚀 快速开始
-----------
1. 双击"C标准分析工具.exe"启动程序
2. 选择需要的分析模块（点击对应标签页）
3. 点击"浏览"按钮选择数据文件
4. 点击"分析"按钮开始分析
5. 查看分析结果和图表

📊 各模块详细说明
-----------------

【CPU分析模块】
• 功能：分析CPU使用率随时间的变化趋势（仅分析g_stageStability=3的数据）
• 数据格式：JSON格式，包含time、current_CPU、g_stageStability字段
• 示例：{"time": "2025-06-10 18:59:52.123", "current_CPU": 0.255, "g_stageStability": 3}
• 输出：时间序列图表 + 统计信息（平均值、最大值、最小值）

【内存分析模块】
• 功能：分析内存使用量的变化趋势
• 数据格式：JSON格式，包含time、current_memory字段
• 示例：{"time": "2025-06-10 18:59:52.123", "current_memory": 150.2}
• 输出：内存使用趋势图 + 内存使用统计

【性能分析模块】
• 功能：分析处理时间（costTime）的变化趋势
• 数据格式：JSON格式，包含time、costTime字段
• 示例：{"time": "2025-06-10 18:59:52.123", "costTime": 15.5}
• 输出：性能趋势图 + 处理时间统计

🖼️ 图表操作
-----------
【复制图表】
• 方法1：点击"复制图表"按钮
• 方法2：在图表上右键选择"复制图表"
• 复制后可直接粘贴到Word、PowerPoint等应用

【保存图表】
• 在图表上右键选择"保存图表"
• 支持PNG、PDF、SVG、JPEG等格式
• 可选择保存位置和文件名

📁 数据文件准备
---------------
【文件格式要求】
• 所有分析模块：每行一个JSON对象
• 文件编码：UTF-8（推荐）
• 文件扩展名：.txt

【示例数据文件】
CPU数据文件内容：
{"time": "2025-06-10 18:59:52.123", "current_CPU": 0.255, "g_stageStability": 3}
{"time": "2025-06-10 18:59:53.124", "current_CPU": 0.283, "g_stageStability": 3}

内存数据文件内容：
{"time": "2025-06-10 18:59:52.123", "current_memory": 150.2}
{"time": "2025-06-10 18:59:53.124", "current_memory": 152.5}

性能数据文件内容：
{"time": "2025-06-10 18:59:52.123", "costTime": 15.5}
{"time": "2025-06-10 18:59:53.124", "costTime": 16.1}

⚠️ 注意事项
-----------
• CPU分析只处理g_stageStability=3的数据行
• current_CPU字段会自动转换为百分比（乘以100）
• 确保数据文件格式正确，否则可能无法解析
• 大文件分析可能需要较长时间，请耐心等待
• 建议在分析前备份原始数据文件

❓ 常见问题解答
---------------
Q: 程序无法启动？
A: 请确保系统是Windows 10或更高版本

Q: 提示"没有有效数据"？
A: 请检查数据文件格式是否正确，参考上述示例格式

Q: CPU分析没有数据？
A: 请确认数据中包含g_stageStability=3的行

Q: 图表显示异常？
A: 可能是数据量过大或格式问题，建议检查数据文件

Q: 复制图表失败？
A: 可以使用"保存图表"功能保存到文件后手动复制

🔧 系统要求
-----------
• 操作系统：Windows 10 或更高版本
• 内存：建议4GB以上
• 磁盘空间：至少100MB可用空间
• 显示器：建议1920x1080分辨率

📞 技术支持
-----------
如遇到使用问题，请提供：
1. 错误信息截图
2. 使用的数据文件示例
3. 操作步骤描述
4. 系统环境信息

===============================================
版本信息：v1.0
更新日期：2024年12月
适用项目：C标准土耳其
===============================================
