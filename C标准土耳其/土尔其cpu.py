import json
import matplotlib.pyplot as plt
from datetime import datetime

# 设置中文字体，避免乱码（如缺字体可改为 SimHei）
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
file_path = r"G:\24042土耳其校车\stability_20250610_185952\stability_CPU_20250610_185952.txt"

# 存储时间和CPU使用率
times = []
cpu_usages = []

# 读取并处理文件
with open(file_path, "r", encoding="utf-8") as file:
    for line in file:
        try:
            data = json.loads(line.strip())
            if data.get("g_stageStability") == 3:
                cpu = data.get("current_CPU", 0) * 100  # 转换为百分比
                time_str = data.get("time", "")
                time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                times.append(time_obj)
                cpu_usages.append(cpu)
        except Exception as e:
            continue  # 忽略异常行

# 如果没有数据，退出
if not times or not cpu_usages:
    print("无可用数据绘图")
    exit()

# 计算平均 CPU 使用率
avg_cpu = sum(cpu_usages) / len(cpu_usages)

# 绘图
plt.figure(figsize=(14, 6))
plt.plot(times, cpu_usages, label='CPU 使用率 (%)', color='blue', linewidth=1.5)
plt.axhline(avg_cpu, color='red', linestyle='--', label=f'平均值：{avg_cpu:.2f}%')

# 图形设置
plt.title("CPU 使用率变化（g_stageStability = 3）", fontsize=14)
plt.xlabel("时间", fontsize=12)
plt.ylabel("使用率 (%)", fontsize=12)
plt.ylim(0, 100)
plt.xticks(rotation=45)  # 旋转x轴时间刻度
plt.legend()
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()

# 显示数据点数量
print(f"共计绘制点数：{len(cpu_usages)}")
plt.show()
