import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics

# 设置中文字体，防止中文乱码
plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def plot_memory_usage(filename):
    times = []
    memory_values = []

    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                data = json.loads(line)
                time_str = data.get("time")
                mem_mb = data.get("current_memory")  # 这里就是MB单位
                if time_str and mem_mb is not None:
                    time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                    times.append(time_obj)
                    memory_values.append(mem_mb)
            except Exception as e:
                print(f"跳过无效行: {e}")

    if not times:
        print("没有数据")
        return

    avg_mem = statistics.mean(memory_values)

    plt.figure(figsize=(12, 6))
    plt.plot(times, memory_values, marker='o', linestyle='-', color='blue', label='内存占用 (MB)')
    plt.axhline(avg_mem, color='orange', linestyle='--', label=f'平均内存占用: {avg_mem:.2f} MB')
    plt.xlabel("时间")
    plt.ylabel("内存占用 (MB)")
    plt.title("内存占用随时间变化")
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.gcf().autofmt_xdate()
    plt.show()

file_path = r"G:\24042土耳其校车\stability_20250610_185952\stability_Memory_20250610_185952.txt"
plot_memory_usage(file_path)
