import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics

# 防止中文乱码和负号显示异常
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False   # 负号正常显示

def plot_cost_time(filename):
    times = []
    cost_times = []

    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                data = json.loads(line)
                time_str = data.get("time")
                cost_time = data.get("costTime")
                if time_str and cost_time is not None:
                    time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                    times.append(time_obj)
                    cost_times.append(cost_time)
            except Exception as e:
                print(f"跳过无效行: {e}")

    if not times:
        print("没有数据")
        return

    avg_cost_time = statistics.mean(cost_times)

    plt.figure(figsize=(12, 6))
    plt.plot(times, cost_times, marker='o', linestyle='-', color='green', label='处理时间 (costTime, ms)')
    plt.axhline(avg_cost_time, color='orange', linestyle='--', alpha=0.8, label=f'平均处理时间: {avg_cost_time:.2f} ms')
    plt.xlabel("时间")
    plt.ylabel("处理时间 (毫秒)")
    plt.title("costTime 随时间的变化")
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.gcf().autofmt_xdate()
    plt.show()

file_path = r"G:\24042土耳其校车\stability_20250610_185952\stability_Detect_20250610_185952.txt"
plot_cost_time(file_path)
