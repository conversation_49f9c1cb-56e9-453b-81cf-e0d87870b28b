#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DMS算法加密检测工具
根据日志文件分析模型算法的加密情况
"""

import re
import os

def analyze_dms_encryption(log_file_path):
    """
    分析DMS日志文件中的算法加密情况
    
    步骤：
    1. 查找"model type is"行，提取size第二列数据并排序
    2. 查找"[CRYPT_INFO]"行，提取size数据并排序  
    3. 进行1对1匹配消除（普通模型size相等，NPU模型相差32字节）
    4. 分析未匹配的算法是否都小于10KB
    """
    
    print("=== DMS算法加密检测工具 ===")
    print(f"分析日志文件: {log_file_path}")
    
    if not os.path.exists(log_file_path):
        print(f"错误：日志文件不存在 - {log_file_path}")
        return False
    
    # 存储解析结果
    model_algorithms = []  # (size, line_number, line_content)
    crypt_algorithms = []  # (size, line_number, line_content)
    
    print("\n正在解析日志文件...")
    
    # 解析日志文件
    try:
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_number, line in enumerate(f, 1):
                line = line.strip()
                
                # 查找"model type is"行
                if "model type is" in line:
                    # 匹配格式: model type is [x-y/z], size:{数字1, 数字2}
                    match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                    if match:
                        size = int(match.group(2))  # 使用第二列数字
                        model_algorithms.append((size, line_number, line))
                        print(f"  模型算法 [行{line_number}]: {size} Bytes")
                
                # 查找"[CRYPT_INFO]"行
                elif "[CRYPT_INFO]" in line:
                    # 匹配格式: [CRYPT_INFO] ... size [数字] ...
                    match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                    if match:
                        size = int(match.group(1))
                        crypt_algorithms.append((size, line_number, line))
                        print(f"  加密算法 [行{line_number}]: {size} Bytes")
    
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
        return False
    
    print(f"\n解析完成:")
    print(f"- 找到 {len(model_algorithms)} 个模型算法")
    print(f"- 找到 {len(crypt_algorithms)} 个加密算法")
    
    if not model_algorithms:
        print("未找到任何模型算法信息")
        return False
    
    if not crypt_algorithms:
        print("未找到任何加密算法信息")
        return False
    
    # 步骤2：按size从小到大排序
    model_algorithms.sort(key=lambda x: x[0])
    crypt_algorithms.sort(key=lambda x: x[0])
    
    print("\n=== 步骤2：排序后的算法列表 ===")
    print("模型算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(model_algorithms, 1):
        print(f"  {i:2d}. {size:8d} Bytes [行{line_num}]")
    
    print("\n加密算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(crypt_algorithms, 1):
        print(f"  {i:2d}. {size:8d} Bytes [行{line_num}]")
    
    # 步骤3：进行1对1匹配消除
    print("\n=== 步骤3：算法匹配过程 ===")
    
    model_sizes = [alg[0] for alg in model_algorithms]
    crypt_sizes = [alg[0] for alg in crypt_algorithms]
    
    unmatched_models = []
    used_crypt_indices = set()
    matched_pairs = []
    
    for model_size in model_sizes:
        matched = False
        
        for i, crypt_size in enumerate(crypt_sizes):
            if i in used_crypt_indices:
                continue
            
            # 检查匹配条件
            if model_size == crypt_size:
                # 普通模型：size完全相等
                used_crypt_indices.add(i)
                matched = True
                matched_pairs.append((model_size, crypt_size, "普通模型"))
                print(f"  ✓ 匹配: 模型{model_size} = 加密{crypt_size} (普通模型)")
                break
            elif model_size == crypt_size + 32:
                # NPU模型：模型size = 加密size + 32
                used_crypt_indices.add(i)
                matched = True
                matched_pairs.append((model_size, crypt_size, "NPU模型"))
                print(f"  ✓ 匹配: 模型{model_size} = 加密{crypt_size}+32 (NPU模型)")
                break
        
        if not matched:
            unmatched_models.append(model_size)
            print(f"  ✗ 未匹配: 模型{model_size} (未加密)")
    
    print(f"\n匹配结果统计:")
    print(f"  - 总模型数量: {len(model_sizes)}")
    print(f"  - 总加密数量: {len(crypt_sizes)}")
    print(f"  - 成功匹配: {len(matched_pairs)}")
    print(f"  - 未加密算法: {len(unmatched_models)}")
    
    # 步骤4：分析未加密算法
    print("\n=== 步骤4：结果A分析 ===")
    
    if not unmatched_models:
        print("🎉 结果A：所有算法都已加密！")
        return True
    
    print("未加密算法列表:")
    
    kb_threshold = 10 * 1024  # 10KB = 10240 Bytes
    all_under_10kb = True
    
    for i, size in enumerate(unmatched_models, 1):
        size_kb = size / 1024
        status = "✓ 符合要求" if size < kb_threshold else "✗ 超出限制"
        print(f"  {i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) - {status}")
        
        if size >= kb_threshold:
            all_under_10kb = False
    
    print(f"\n{'='*60}")
    print("最终结果A:")
    print(f"  - 未加密算法数量: {len(unmatched_models)}")
    print(f"  - 10KB阈值: {kb_threshold} Bytes")
    
    if all_under_10kb:
        print("  ✅ 结论：所有未加密的算法都小于10KB - 符合安全要求")
    else:
        print("  ❌ 结论：存在大于等于10KB的未加密算法 - 不符合安全要求")
        large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
        print(f"     超出10KB限制的算法: {len(large_algorithms)} 个")
        for size in large_algorithms:
            print(f"       - {size} Bytes ({size/1024:.2f} KB)")
    
    return all_under_10kb

def main():
    """主函数"""
    # 默认日志文件路径
    log_file = "test_log.log"
    
    # 如果文件不存在，提示用户
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在")
        print("请将DMS日志文件重命名为 'test_log.log' 并放在脚本同目录下")
        return
    
    # 执行分析
    result = analyze_dms_encryption(log_file)
    
    print(f"\n分析完成，结果: {'通过' if result else '不通过'}")

if __name__ == "__main__":
    main()
