# memory_plot.py
import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics
import matplotlib.dates as mdates

file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\使用工具后没崩溃\YiYuan450_1.0.032.1101.72_20250311_021708\YiYuan450\cpu_mem_log.txt"

time_list, native_pss_list, total_pss_list = [], [], []

with open(file_path, 'r', encoding='utf-8') as f:
    for line in f:
        data = json.loads(line)
        mem = data['MemoryUsage']
        time_list.append(datetime.strptime(data['time'], '%Y-%m-%d %H:%M:%S.%f'))
        native_pss_list.append(mem['NativePss'])
        total_pss_list.append(mem['TotalPss'])

avg_native_pss = statistics.mean(native_pss_list)
avg_total_pss  = statistics.mean(total_pss_list)

plt.figure(figsize=(12, 5))

# NativePss - 黄色圆点
plt.plot(time_list, native_pss_list, marker='o', color='gold', label='NativePss (MB)')
plt.axhline(avg_native_pss, color='gold', linestyle='--', alpha=0.6, label=f'Avg NativePss: {avg_native_pss:.2f} MB')

# TotalPss - 蓝色圆点
plt.plot(time_list, total_pss_list, marker='o', color='blue', label='TotalPss (MB)')
plt.axhline(avg_total_pss, color='blue', linestyle='--', alpha=0.6, label=f'Avg TotalPss: {avg_total_pss:.2f} MB')

# 时间轴优化
ax = plt.gca()
ax.xaxis.set_major_locator(mdates.AutoDateLocator())
ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

plt.xlabel('Time')
plt.ylabel('Memory Usage (MB)')
plt.title('NativePss and TotalPss Over Time')
plt.xticks(rotation=45)
plt.ylim(bottom=0)
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()
