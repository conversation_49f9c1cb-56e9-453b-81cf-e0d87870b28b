import json

def analyze_cost_time_from_file(file_path, max_lines=10000):
    cost_times = []
    line_count = 0

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line_count >= max_lines:
                break
            line_count += 1
            try:
                data = json.loads(line.strip())
                if 'costTime' in data:
                    cost_times.append(data['costTime'])
            except json.JSONDecodeError:
                print(f"跳过格式错误的行：{line.strip()}")

    if not cost_times:
        print("没有找到任何 costTime 数据。")
        return

    max_val = max(cost_times)
    min_val = min(cost_times)
    avg_val = sum(cost_times) / len(cost_times)

    print(f"共分析前 {len(cost_times)} 条包含 costTime 的数据")
    print(f"最大 costTime：{max_val} ms")
    print(f"最小 costTime：{min_val} ms")
    print(f"平均 costTime：{avg_val:.2f} ms")

# ✅ 指定完整文件路径
file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\Performance\YiYuan450_1.0.032.1101.72_20250311_021708\stability_detect_EIS_1.0.032.1101.72.txt"
analyze_cost_time_from_file(file_path, max_lines=40000)
