import re
import matplotlib.pyplot as plt
from datetime import datetime

# 设置文件路径
file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\结果\emeResult.txt"

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as file:
    lines = file.readlines()

# 提取时间数据和 Native Heap 的第一个值
timestamps = []
native_heap_values = []
current_timestamp = None

for line in lines:
    # 提取时间数据
    time_match = re.match(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]', line)
    if time_match:
        time_str = time_match.group(1)
        current_timestamp = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

    # 提取 Native Heap 的第一个值
    if 'Native Heap' in line and current_timestamp is not None and 'Java Heap' not in line:
        # 使用正则表达式匹配 "Native Heap" 后面的第一个数值
        value_match = re.search(r'Native Heap\s+(\d+)', line)
        if value_match:
            # 将 KB 转换为 MB
            native_heap_values.append(int(value_match.group(1)) / 1024)
            timestamps.append(current_timestamp)
            current_timestamp = None  # 重置 current_timestamp，避免重复使用

# 计算平均值
if native_heap_values:
    average_memory_usage = sum(native_heap_values) / len(native_heap_values)
else:
    average_memory_usage = 0

# 绘制折线图
plt.figure(figsize=(10, 5))
plt.plot(timestamps, native_heap_values, marker='o', label='Native Heap Memory Usage (MB)')

# 绘制平均值的水平线
plt.axhline(y=average_memory_usage, color='r', linestyle='--', label=f'Average Memory Usage: {average_memory_usage:.2f} MB')

# 设置图表标题和坐标轴标签
plt.title('Native Heap Memory Usage Over Time')
plt.xlabel('Timestamp')
plt.ylabel('Native Heap Memory Usage (MB)')

# 设置网格线
plt.grid(True)

# 设置纵坐标从 0 开始，并根据数据的最大值设置上限
if native_heap_values:
    max_value = max(native_heap_values)
    plt.ylim(0, max_value * 1.1)  # 设置上限为最大值的 110%

# 自动调整日期格式，保证横坐标标签的可读性
plt.gcf().autofmt_xdate()

# 添加图例
plt.legend()

# 保存图表为文件
plt.savefig('native_heap_memory_usage_over_time.png')
print("图表已保存为 native_heap_memory_usage_over_time.png")

# 打印平均值
print(f"平均内存使用量: {average_memory_usage:.2f} MB")
