import pandas as pd
import matplotlib.pyplot as plt

def read_and_plot_cpu_usage(file_path, max_cpu_usage):
    # 读取文件
    try:
        data = pd.read_json(file_path, lines=True)
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return

    # 检查数据是否为空
    if data.empty:
        print("文件中没有数据。")
        return

    # 将时间戳转换为 datetime 类型
    data['timestamp'] = pd.to_datetime(data['timestamp'])

    # 设置时间戳为索引
    data.set_index('timestamp', inplace=True)

    # 调整 CPU 使用百分比
    data['cpu_usage'] = (data['cpu_usage'] / max_cpu_usage) * 100

    # 计算平均值
    average_cpu_usage = data['cpu_usage'].mean()

    # 绘制 CPU 使用量图表
    plt.figure(figsize=(12, 6))
    plt.plot(data.index, data['cpu_usage'], label='CPU Usage (%)', color='r')
    plt.axhline(y=average_cpu_usage, color='b', linestyle='--', label=f'Average CPU Usage: {average_cpu_usage:.2f}%')
    plt.title('CPU Usage Over Time')
    plt.xlabel('Time')
    plt.ylabel('CPU Usage (%)')
    plt.grid(True)
    plt.legend()
    plt.ylim(0, 100)  # 设置纵坐标从 0 开始到 100
    plt.tight_layout()
    plt.show()

    # 打印平均值
    print(f"平均 CPU 使用量: {average_cpu_usage:.2f}%")

if __name__ == "__main__":
    file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\结果\thread_result.txt" # 使用原始字符串
    # 或者使用双反斜杠
    # file_path = "G:\\EIS测试\\移远素材\\离线激活和bin文件\\脚本\\内存结果\\内存泄漏\\thread_result.txt"
    max_cpu_usage = 800  # 文件中的最大 CPU 使用百分比
    read_and_plot_cpu_usage(file_path, max_cpu_usage)