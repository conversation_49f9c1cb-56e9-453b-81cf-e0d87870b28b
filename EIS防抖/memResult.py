import pandas as pd
import matplotlib.pyplot as plt

def read_and_plot_memory_usage(file_path, total_memory_kb):
    # 读取文件
    try:
        data = pd.read_json(file_path, lines=True)
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return

    # 检查数据是否为空
    if data.empty:
        print("文件中没有数据。")
        return

    # 将时间戳转换为 datetime 类型
    data['timestamp'] = pd.to_datetime(data['timestamp'])

    # 设置时间戳为索引
    data.set_index('timestamp', inplace=True)

    # 将内存使用百分比转换为实际内存使用量（MB）
    data['mem_usage_mb'] = (data['mem_usage_mb'] / 100) * (total_memory_kb / 1024)

    # 计算平均值
    average_memory_usage = data['mem_usage_mb'].mean()

    # 绘制内存使用量图表
    plt.figure(figsize=(12, 6))
    plt.plot(data.index, data['mem_usage_mb'], label='Memory Usage (MB)', color='b')
    plt.axhline(y=average_memory_usage, color='r', linestyle='--', label=f'Average Memory Usage: {average_memory_usage:.2f} MB')
    plt.title('Memory Usage Over Time')
    plt.xlabel('Time')
    plt.ylabel('Memory Usage (MB)')
    plt.grid(True)
    plt.legend()
    plt.ylim(0, None)  # 设置纵坐标从 0 开始
    plt.tight_layout()
    plt.show()

    # 打印平均值
    print(f"平均内存使用量: {average_memory_usage:.2f} MB")

if __name__ == "__main__":
    file_path = r"G:\EIS工具升级后测试\移远素材\1.0.032.1101.72\性能测试\结果\thread_result.txt"  # 使用原始字符串
    # 或者使用双反斜杠
    # file_path = "G:\\EIS测试\\移远素材\\离线激活和bin文件\\脚本\\内存结果\\内存泄漏\\thread_result.txt"
    total_memory_kb = 1869516  # 总内存大小（KB）
    read_and_plot_memory_usage(file_path, total_memory_kb)
