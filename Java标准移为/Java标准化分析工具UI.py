#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java标准化分析工具UI
统一的可视化界面，集成CPU、内存、性能和模型加密分析功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from datetime import datetime
import statistics
import matplotlib.font_manager as fm
from collections import defaultdict
import re
import os
import sys
import threading
from io import StringIO, BytesIO
import base64
from PIL import Image

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class JavaAnalysisToolUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Java标准化分析工具")
        self.root.geometry("1200x800")

        # 创建主框架
        self.create_widgets()

        # 存储分析结果
        self.cpu_data = None
        self.memory_data = None
        self.performance_data = None
        self.encryption_result = None

        # 存储图表对象，用于复制功能
        self.cpu_figure = None
        self.memory_figure = None

    def create_widgets(self):
        """创建UI组件"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建各个功能标签页
        self.create_cpu_tab()
        self.create_memory_tab()
        self.create_performance_tab()
        self.create_encryption_tab()

    def create_cpu_tab(self):
        """创建CPU分析标签页"""
        cpu_frame = ttk.Frame(self.notebook)
        self.notebook.add(cpu_frame, text="CPU分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(cpu_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.cpu_file_var = tk.StringVar()
        ttk.Label(file_frame, text="数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.cpu_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_cpu_file).pack(side=tk.RIGHT, padx=(5,0))

        # 控制按钮
        control_frame = ttk.Frame(cpu_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析CPU数据", command=self.analyze_cpu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_cpu_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_cpu_chart).pack(side=tk.LEFT, padx=5)

        # 图表区域
        self.cpu_chart_frame = ttk.LabelFrame(cpu_frame, text="CPU使用率图表", padding=10)
        self.cpu_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(cpu_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)

        self.cpu_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.cpu_result_text.pack(fill=tk.BOTH, expand=True)

    def create_memory_tab(self):
        """创建内存分析标签页"""
        memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(memory_frame, text="内存分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(memory_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.memory_file_var = tk.StringVar()
        ttk.Label(file_frame, text="数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.memory_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_memory_file).pack(side=tk.RIGHT, padx=(5,0))

        # 控制按钮
        control_frame = ttk.Frame(memory_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析内存数据", command=self.analyze_memory).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_memory_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_memory_chart).pack(side=tk.LEFT, padx=5)

        # 图表区域
        self.memory_chart_frame = ttk.LabelFrame(memory_frame, text="内存使用趋势图", padding=10)
        self.memory_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(memory_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)

        self.memory_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.memory_result_text.pack(fill=tk.BOTH, expand=True)

    def create_performance_tab(self):
        """创建性能分析标签页"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="性能分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(performance_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.performance_file_var = tk.StringVar()
        ttk.Label(file_frame, text="数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.performance_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_performance_file).pack(side=tk.RIGHT, padx=(5,0))

        # 控制按钮
        control_frame = ttk.Frame(performance_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析性能数据", command=self.analyze_performance).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除结果", command=self.clear_performance_result).pack(side=tk.LEFT, padx=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(performance_frame, text="性能分析结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.performance_result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD)
        self.performance_result_text.pack(fill=tk.BOTH, expand=True)

    def create_encryption_tab(self):
        """创建模型加密分析标签页"""
        encryption_frame = ttk.Frame(self.notebook)
        self.notebook.add(encryption_frame, text="模型加密分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(encryption_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.encryption_file_var = tk.StringVar()
        ttk.Label(file_frame, text="日志文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.encryption_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_encryption_file).pack(side=tk.RIGHT, padx=(5,0))

        # 控制按钮
        control_frame = ttk.Frame(encryption_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析模型加密", command=self.analyze_encryption).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除结果", command=self.clear_encryption_result).pack(side=tk.LEFT, padx=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(encryption_frame, text="模型加密分析结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.encryption_result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD)
        self.encryption_result_text.pack(fill=tk.BOTH, expand=True)

    # 文件浏览方法
    def browse_cpu_file(self):
        filename = filedialog.askopenfilename(
            title="选择CPU数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.cpu_file_var.set(filename)

    def browse_memory_file(self):
        filename = filedialog.askopenfilename(
            title="选择内存数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.memory_file_var.set(filename)

    def browse_performance_file(self):
        filename = filedialog.askopenfilename(
            title="选择性能数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.performance_file_var.set(filename)

    def browse_encryption_file(self):
        filename = filedialog.askopenfilename(
            title="选择模型加密日志文件",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.encryption_file_var.set(filename)

    # 图表复制功能
    def copy_figure_to_clipboard(self, figure):
        """将matplotlib图表复制到剪贴板"""
        try:
            # 尝试使用win32clipboard
            try:
                import win32clipboard
                from PIL import Image

                # 将图表保存到内存中的字节流
                buf = BytesIO()
                figure.savefig(buf, format='png', dpi=150, bbox_inches='tight',
                             facecolor='white', edgecolor='none')
                buf.seek(0)

                # 使用PIL打开图像
                img = Image.open(buf)

                # 将图像复制到剪贴板
                output = BytesIO()
                img.convert('RGB').save(output, 'BMP')
                data = output.getvalue()[14:]  # BMP文件头是14字节
                output.close()
                buf.close()

                # 复制到Windows剪贴板
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                win32clipboard.CloseClipboard()

                messagebox.showinfo("成功", "图表已复制到剪贴板！\n可以直接粘贴到Word、PowerPoint等应用中")

            except ImportError:
                # 如果没有win32clipboard，使用tkinter的方法
                self.copy_figure_tkinter(figure)

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def copy_figure_tkinter(self, figure):
        """使用tkinter的剪贴板复制方法"""
        try:
            import tempfile
            import os

            # 保存图表到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            figure.savefig(temp_file.name, format='png', dpi=150, bbox_inches='tight',
                         facecolor='white', edgecolor='none')
            temp_file.close()

            # 尝试使用系统命令复制图片到剪贴板
            try:
                # Windows PowerShell命令复制图片到剪贴板
                import subprocess
                cmd = f'powershell.exe "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Clipboard]::SetImage([System.Drawing.Image]::FromFile(\'{temp_file.name}\'))"'
                subprocess.run(cmd, shell=True, check=True)

                # 删除临时文件
                os.unlink(temp_file.name)

                messagebox.showinfo("成功", "图表已复制到剪贴板！")

            except:
                # 如果PowerShell方法失败，提示用户手动复制
                messagebox.showinfo("图表已保存",
                                  f"图表已保存到临时文件:\n{temp_file.name}\n\n"
                                  f"请手动打开此文件并复制到剪贴板")

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def create_chart_context_menu(self, canvas, figure):
        """为图表创建右键菜单"""
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="复制图表", command=lambda: self.copy_figure_to_clipboard(figure))
        context_menu.add_command(label="保存图表", command=lambda: self.save_figure_to_file(figure))

        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        canvas.get_tk_widget().bind("<Button-3>", show_context_menu)  # 右键
        return context_menu

    def save_figure_to_file(self, figure):
        """保存图表到文件"""
        try:
            # 获取当前时间作为默认文件名
            from datetime import datetime
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"图表_{current_time}.png"

            filename = filedialog.asksaveasfilename(
                title="保存图表到文件",
                initialname=default_filename,
                defaultextension=".png",
                filetypes=[
                    ("PNG图片", "*.png"),
                    ("PDF文档", "*.pdf"),
                    ("SVG矢量图", "*.svg"),
                    ("JPEG图片", "*.jpg"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                # 根据文件扩展名确定格式
                file_ext = filename.lower().split('.')[-1]
                if file_ext in ['png', 'jpg', 'jpeg']:
                    figure.savefig(filename, dpi=300, bbox_inches='tight',
                                 facecolor='white', edgecolor='none')
                else:
                    figure.savefig(filename, bbox_inches='tight',
                                 facecolor='white', edgecolor='none')

                messagebox.showinfo("保存成功", f"图表已保存到:\n{filename}")

                # 询问是否打开文件所在文件夹
                if messagebox.askyesno("打开文件夹", "是否打开文件所在的文件夹？"):
                    import os
                    import subprocess
                    folder_path = os.path.dirname(filename)
                    subprocess.Popen(f'explorer "{folder_path}"')

        except Exception as e:
            messagebox.showerror("保存失败", f"保存图表失败: {str(e)}")

    # CPU分析方法
    def analyze_cpu(self):
        """分析CPU数据"""
        file_path = self.cpu_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择CPU数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            # 在后台线程中执行分析
            threading.Thread(target=self._analyze_cpu_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_cpu_thread(self, file_path):
        """CPU分析线程"""
        try:
            times = []
            cpu_usages = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if "CpuUsage" in data and "time" in data:
                            times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                            cpu_usages.append(float(data["CpuUsage"]))
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的CPU数据"))
                return

            # 计算统计信息
            avg_cpu = statistics.mean(cpu_usages)
            max_cpu = max(cpu_usages)
            min_cpu = min(cpu_usages)

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_cpu_ui(times, cpu_usages, avg_cpu, max_cpu, min_cpu))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_cpu_ui(self, times, cpu_usages, avg_cpu, max_cpu, min_cpu):
        """更新CPU分析UI"""
        # 清除之前的图表
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(times, cpu_usages, color='red', label='CPU 使用率 (%)', marker='o', markersize=3)
        ax.axhline(avg_cpu, color='red', linestyle='--', alpha=0.6, label=f'平均值: {avg_cpu:.2f}%')

        ax.set_xlabel("时间")
        ax.set_ylabel("CPU 使用率 (%)")
        ax.set_title("CPU 使用率随时间变化")
        ax.legend()
        ax.grid(True)
        ax.set_ylim(0, 100)
        fig.autofmt_xdate()

        # 保存图表对象
        self.cpu_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.cpu_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""CPU分析结果：
数据点数量: {len(cpu_usages)}
平均CPU使用率: {avg_cpu:.2f}%
最大CPU使用率: {max_cpu:.2f}%
最小CPU使用率: {min_cpu:.2f}%
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_result_text.insert(tk.END, result_text)

    def copy_cpu_chart(self):
        """复制CPU图表"""
        if self.cpu_figure:
            self.copy_figure_to_clipboard(self.cpu_figure)
        else:
            messagebox.showwarning("警告", "请先分析CPU数据生成图表")

    def clear_cpu_chart(self):
        """清除CPU图表"""
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()
        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_figure = None

    # 内存分析方法
    def analyze_memory(self):
        """分析内存数据"""
        file_path = self.memory_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择内存数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_memory_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_memory_thread(self, file_path):
        """内存分析线程"""
        try:
            times = []
            total_pss_list = []
            native_pss_list = []

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if "TotalPss" in data and "NativePss" in data and "time" in data:
                            times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                            total_pss_list.append(float(data["TotalPss"]))
                            native_pss_list.append(float(data["NativePss"]))
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的内存数据"))
                return

            # 计算统计信息
            avg_total = statistics.mean(total_pss_list)
            avg_native = statistics.mean(native_pss_list)
            max_total = max(total_pss_list)
            max_native = max(native_pss_list)

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_memory_ui(times, total_pss_list, native_pss_list,
                                                            avg_total, avg_native, max_total, max_native))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_memory_ui(self, times, total_pss_list, native_pss_list, avg_total, avg_native, max_total, max_native):
        """更新内存分析UI"""
        # 清除之前的图表
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(times, total_pss_list, label=f'TotalPss (MB)', color='blue', linestyle='-', marker='o', markersize=3)
        ax.plot(times, native_pss_list, label=f'NativePss (MB)', color='orange', linestyle='-', marker='o', markersize=3)

        # 平均线
        ax.axhline(avg_total, color='blue', linestyle='--', alpha=0.6, label=f'TotalPss 平均值: {avg_total:.2f} MB')
        ax.axhline(avg_native, color='orange', linestyle='--', alpha=0.6, label=f'NativePss 平均值: {avg_native:.2f} MB')

        ax.set_xlabel("时间")
        ax.set_ylabel("内存使用量 (MB)")
        ax.set_title("内存使用趋势图")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        # 保存图表对象
        self.memory_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.memory_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""内存分析结果：
数据点数量: {len(total_pss_list)}
TotalPss 平均值: {avg_total:.2f} MB
TotalPss 最大值: {max_total:.2f} MB
NativePss 平均值: {avg_native:.2f} MB
NativePss 最大值: {max_native:.2f} MB
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.memory_result_text.delete(1.0, tk.END)
        self.memory_result_text.insert(tk.END, result_text)

    def copy_memory_chart(self):
        """复制内存图表"""
        if self.memory_figure:
            self.copy_figure_to_clipboard(self.memory_figure)
        else:
            messagebox.showwarning("警告", "请先分析内存数据生成图表")

    def clear_memory_chart(self):
        """清除内存图表"""
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()
        self.memory_result_text.delete(1.0, tk.END)
        self.memory_figure = None

    # 性能分析方法
    def analyze_performance(self):
        """分析性能数据"""
        file_path = self.performance_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择性能数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_performance_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_performance_thread(self, file_path):
        """性能分析线程"""
        try:
            module_stats = defaultdict(list)
            total_lines = 0
            valid_lines = 0

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    total_lines += 1
                    try:
                        data = json.loads(line.strip())

                        # 尝试多种字段名组合（兼容不同格式）
                        module = data.get("module") or data.get("Module")
                        cost_time = data.get("costTime") or data.get("CostTime")

                        if module and isinstance(cost_time, (int, float)):
                            module_stats[module].append(float(cost_time))
                            valid_lines += 1

                    except json.JSONDecodeError:
                        # 跳过非JSON行
                        continue
                    except Exception as e:
                        # 跳过其他错误行
                        continue

            print(f"调试信息: 总行数={total_lines}, 有效行数={valid_lines}, 模块数={len(module_stats)}")

            if not module_stats:
                error_msg = f"没有有效的性能数据\n总行数: {total_lines}\n有效行数: {valid_lines}\n请检查文件格式是否正确"
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                return

            # 计算统计信息
            results = {}
            for module, times in module_stats.items():
                results[module] = {
                    'count': len(times),
                    'total': sum(times),
                    'avg': statistics.mean(times),
                    'max': max(times),
                    'min': min(times)
                }

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_performance_ui(results, total_lines, valid_lines))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_performance_ui(self, results, total_lines=0, valid_lines=0):
        """更新性能分析UI"""
        # 生成结果文本
        result_text = "=== 性能分析结果 ===\n\n"

        # 添加文件解析信息
        result_text += f"文件解析信息:\n"
        result_text += f"  总行数: {total_lines}\n"
        result_text += f"  有效数据行数: {valid_lines}\n"
        result_text += f"  识别的模块数: {len(results)}\n\n"

        if not results:
            result_text += "未找到任何有效的性能数据。\n"
            result_text += "请检查文件格式是否为JSON格式，且包含 'module' 和 'costTime' 字段。\n"
            self.performance_result_text.delete(1.0, tk.END)
            self.performance_result_text.insert(tk.END, result_text)
            return

        # 使用固定宽度的表格格式，确保中英文对齐
        col_widths = [20, 10, 15, 15, 15, 15]  # 各列宽度

        # 表头
        header_parts = ["模块名称", "次数", "总耗时(ms)", "平均耗时(ms)", "最大耗时(ms)", "最小耗时(ms)"]
        header_line = ""
        for i, (part, width) in enumerate(zip(header_parts, col_widths)):
            if i == 0:
                header_line += f"{part:<{width}}"
            else:
                header_line += f" | {part:<{width-3}}"

        result_text += header_line + "\n"
        result_text += "-" * len(header_line) + "\n"

        # 按平均耗时排序
        sorted_results = sorted(results.items(), key=lambda x: x[1]['avg'], reverse=True)

        for module, stats in sorted_results:
            # 处理模块名长度
            if len(module) > col_widths[0]:
                module_display = module[:col_widths[0]-3] + "..."
            else:
                module_display = module

            # 数据行
            data_parts = [
                module_display,
                str(stats['count']),
                f"{stats['total']:.2f}",
                f"{stats['avg']:.2f}",
                f"{stats['max']:.2f}",
                f"{stats['min']:.2f}"
            ]

            data_line = ""
            for i, (part, width) in enumerate(zip(data_parts, col_widths)):
                if i == 0:
                    data_line += f"{part:<{width}}"
                else:
                    data_line += f" | {part:<{width-3}}"

            result_text += data_line + "\n"

        result_text += "\n=== 统计摘要 ===\n"
        result_text += f"总模块数量: {len(results)}\n"
        result_text += f"总调用次数: {sum(stats['count'] for stats in results.values())}\n"
        result_text += f"总耗时: {sum(stats['total'] for stats in results.values()):.2f} ms\n"

        # 找出耗时最长的模块
        if sorted_results:
            slowest_module, slowest_stats = sorted_results[0]
            result_text += f"平均耗时最长的模块: {slowest_module} ({slowest_stats['avg']:.2f} ms)\n"

        self.performance_result_text.delete(1.0, tk.END)
        self.performance_result_text.insert(tk.END, result_text)

    def clear_performance_result(self):
        """清除性能分析结果"""
        self.performance_result_text.delete(1.0, tk.END)

    # 模型加密分析方法
    def analyze_encryption(self):
        """分析模型加密"""
        file_path = self.encryption_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择模型加密日志文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 显示开始分析的提示
        self.encryption_result_text.delete(1.0, tk.END)
        self.encryption_result_text.insert(tk.END, "正在分析模型加密数据，请稍候...\n")
        self.root.update()  # 强制更新UI

        try:
            # 直接在主线程中执行分析，避免线程问题
            old_stdout = sys.stdout
            captured_output = StringIO()
            sys.stdout = captured_output

            # 调用分析函数
            result = self.analyze_dms_encryption(file_path)

            # 恢复输出
            sys.stdout = old_stdout
            output_text = captured_output.getvalue()

            # 更新UI
            self._update_encryption_ui(output_text, result)

        except Exception as e:
            # 确保恢复stdout
            sys.stdout = old_stdout
            error_msg = f"模型加密分析失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.encryption_result_text.delete(1.0, tk.END)
            self.encryption_result_text.insert(tk.END, f"分析失败: {error_msg}")



    def _update_encryption_ui(self, output_text, result):
        """更新模型加密分析UI"""
        # 添加结果标题
        final_result = "=== 模型加密分析结果 ===\n\n"
        final_result += output_text
        final_result += "\n" + "="*60 + "\n"

        if result:
            final_result += "🟢 最终结果: 通过安全检测\n"
        else:
            final_result += "🔴 最终结果: 未通过安全检测\n"

        self.encryption_result_text.delete(1.0, tk.END)
        self.encryption_result_text.insert(tk.END, final_result)

        # 滚动到底部
        self.encryption_result_text.see(tk.END)

    def clear_encryption_result(self):
        """清除模型加密分析结果"""
        self.encryption_result_text.delete(1.0, tk.END)

    def analyze_dms_encryption(self, log_path):
        """
        算法加密检测工具 - 从原始脚本移植
        """
        print("=== 算法加密检测工具 ===")
        print(f"分析日志文件: {log_path}")

        if not os.path.exists(log_path):
            print(f"错误：日志文件不存在 - {log_path}")
            return False

        # 检查文件信息
        try:
            file_size = os.path.getsize(log_path)
            print(f"文件大小: {file_size} 字节")
            if file_size == 0:
                print("错误：文件为空")
                return False
        except Exception as e:
            print(f"无法获取文件信息: {e}")
            return False

        # 存储解析结果
        model_info = []  # (size, line_number, line_content)
        crypt_info = []  # (size, line_number, line_content)

        print("\n步骤1: 解析日志文件...")

        # 解析日志文件 - 尝试不同编码格式
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'ascii', 'latin-1', 'utf-16']
        file_opened = False

        for encoding in encodings_to_try:
            try:
                print(f"  尝试使用编码: {encoding}")
                with open(log_path, 'r', encoding=encoding, errors='ignore') as f:
                    line_number = 0
                    for line in f:
                        line_number += 1
                        line = line.strip()

                        # 查找包含 "model type is" 的行
                        if "model type is" in line:
                            model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                            if model_match:
                                size = int(model_match.group(2))  # 第二列
                                model_info.append((size, line_number, line))
                                print(f"  找到模型算法 [行{line_number}]: size={size}")

                        # 查找包含 "[CRYPT_INFO]" 的行
                        elif "[CRYPT_INFO]" in line:
                            crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                            if crypt_match:
                                size = int(crypt_match.group(1))
                                crypt_info.append((size, line_number, line))
                                print(f"  找到加密算法 [行{line_number}]: size={size}")

                    print(f"  成功使用编码 {encoding} 读取文件，共 {line_number} 行")
                    file_opened = True
                    break

            except Exception as e:
                print(f"  编码 {encoding} 失败: {e}")
                continue

        if not file_opened:
            print("所有编码格式都无法读取文件")
            return False

        print(f"\n解析完成:")
        print(f"- 找到 {len(model_info)} 个模型算法")
        print(f"- 找到 {len(crypt_info)} 个加密算法")

        if not model_info and not crypt_info:
            print("\n调试信息：未找到任何匹配的行")
            return False

        # 步骤2: 按size大小从小到大排序
        print("\n步骤2: 按size大小从小到大排序...")
        model_info.sort(key=lambda x: x[0])
        crypt_info.sort(key=lambda x: x[0])

        print("\n模型算法 (按size从小到大):")
        for i, (size, line_num, _) in enumerate(model_info, 1):
            print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")

        print("\n加密算法 (按size从小到大):")
        for i, (size, line_num, _) in enumerate(crypt_info, 1):
            print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")

        # 步骤3: 进行1对1匹配消除
        print("\n步骤3: 进行1对1匹配消除...")
        model_sizes = [info[0] for info in model_info]
        crypt_sizes = [info[0] for info in crypt_info]

        unmatched_models = []
        used_crypt_indexes = set()
        matched_pairs = []

        for m_size in model_sizes:
            matched = False
            for j, c_size in enumerate(crypt_sizes):
                if j in used_crypt_indexes:
                    continue

                # 检查两种匹配情况：
                # 1. 普通模型：m_size == c_size
                # 2. NPU模型：m_size == c_size + 32
                if m_size == c_size:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "普通模型"))
                    print(f"  ✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型)")
                    break
                elif m_size == c_size + 32:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "NPU模型"))
                    print(f"  ✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型)")
                    break

            if not matched:
                unmatched_models.append(m_size)
                # 显示该模型与所有加密算法的大小对比
                available_crypt = [c_size for i, c_size in enumerate(crypt_sizes) if i not in used_crypt_indexes]
                if available_crypt:
                    crypt_info_str = ", ".join(map(str, available_crypt[:5]))  # 只显示前5个
                    if len(available_crypt) > 5:
                        crypt_info_str += "..."
                    print(f"  ✗ 未匹配: 模型{m_size} 不等于 加密[{crypt_info_str}] (未加密)")
                else:
                    print(f"  ✗ 未匹配: 模型{m_size} (无可用加密算法匹配)")

        print(f"\n匹配结果统计:")
        print(f"  - 总模型数量: {len(model_sizes)}")
        print(f"  - 总加密数量: {len(crypt_sizes)}")
        print(f"  - 成功匹配: {len(matched_pairs)}")
        print(f"  - 未加密算法: {len(unmatched_models)}")

        # 步骤4: 分析未加密算法
        print("\n步骤4: 分析未加密算法...")

        if not unmatched_models:
            print("结果A：所有算法都已加密！")
            return True

        print("未加密算法列表（单位：Byte）：")

        kb_threshold = 10 * 1024  # 10KB = 10240 Bytes

        for i, size in enumerate(unmatched_models, 1):
            size_kb = size / 1024
            status = "✓ 符合要求" if size < kb_threshold else "✗ 超出限制"
            print(f"  {i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) - {status}")

        print(f"\n{'='*60}")
        print("结果A分析:")
        print(f"  - 未加密算法数量: {len(unmatched_models)}")
        print(f"  - 10KB阈值: {kb_threshold} Bytes")
        print(f"  ❌ 结果A：存在未加密算法 - 不符合安全要求")
        print(f"     未加密算法数量: {len(unmatched_models)} 个")

        return False  # 只要有未加密算法就返回False


def main():
    """主程序入口"""
    root = tk.Tk()
    app = JavaAnalysisToolUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()