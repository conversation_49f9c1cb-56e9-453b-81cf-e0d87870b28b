#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将Java标准化分析工具打包为exe文件
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        return False

def build_exe():
    """构建exe文件"""
    print("正在构建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 不显示控制台窗口
        "--name=Java标准化分析工具",      # exe文件名
        "--icon=icon.ico",              # 图标文件（如果存在）
        "--add-data=requirements.txt;.", # 包含requirements.txt
        "--hidden-import=PIL._tkinter_finder",  # 隐式导入
        "--hidden-import=tkinter",
        "--hidden-import=matplotlib.backends.backend_tkagg",
        "--hidden-import=win32clipboard",
        "--collect-all=matplotlib",     # 收集matplotlib所有文件
        "--collect-all=PIL",            # 收集PIL所有文件
        "Java标准化分析工具UI.py"        # 主程序文件
    ]
    
    try:
        # 如果没有图标文件，移除图标参数
        if not os.path.exists("icon.ico"):
            cmd = [arg for arg in cmd if not arg.startswith("--icon")]
        
        subprocess.check_call(cmd)
        print("✅ exe文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ exe文件构建失败: {e}")
        return False

def create_icon():
    """创建简单的图标文件"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个简单的图标
        size = (64, 64)
        img = Image.new('RGBA', size, (70, 130, 180, 255))  # 钢蓝色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的图标
        draw.rectangle([8, 8, 56, 56], outline=(255, 255, 255, 255), width=2)
        draw.text((16, 20), "Java", fill=(255, 255, 255, 255))
        draw.text((16, 35), "分析", fill=(255, 255, 255, 255))
        
        # 保存为ico文件
        img.save("icon.ico", format='ICO')
        print("✅ 图标文件创建成功")
        return True
    except Exception as e:
        print(f"⚠️ 图标文件创建失败: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    print("正在清理构建文件...")
    
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["Java标准化分析工具.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除目录: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 已删除文件: {file_name}")

def create_user_guide():
    """创建用户使用说明"""
    guide_content = """
# Java标准化分析工具 使用说明

## 📋 工具功能
本工具集成了四个分析模块：
1. **CPU分析** - 分析CPU使用率数据并生成图表
2. **内存分析** - 分析内存使用数据并生成趋势图
3. **性能分析** - 按模块统计性能耗时数据
4. **模型加密分析** - 检测模型算法加密情况

## 🚀 使用方法

### 1. 启动程序
双击 `Java标准化分析工具.exe` 启动程序

### 2. 选择分析模块
程序界面有四个标签页，点击对应标签页进入相应的分析模块

### 3. 导入数据文件
- 点击"浏览"按钮选择数据文件
- 支持的文件格式：.txt, .log, .json等

### 4. 开始分析
点击对应的"分析"按钮开始数据分析

### 5. 查看结果
- **图表区域**：显示生成的图表（CPU和内存分析）
- **结果区域**：显示详细的分析结果

### 6. 复制和保存
- **复制图表**：右键点击图表选择"复制图表"，可直接粘贴到Word、PPT等
- **保存图表**：右键点击图表选择"保存图表"，选择保存位置

## 📁 数据文件格式

### CPU分析数据格式
```json
{"time": "2024-01-01 10:00:00.000", "CpuUsage": 25.5}
```

### 内存分析数据格式
```json
{"time": "2024-01-01 10:00:00.000", "TotalPss": 150.2, "NativePss": 80.1}
```

### 性能分析数据格式
```json
{"module": "FaceDetection", "costTime": 15.5}
```

### 模型加密分析
支持包含以下关键字的日志文件：
- "model type is" - 模型算法信息
- "[CRYPT_INFO]" - 加密算法信息

## ❓ 常见问题

**Q: 程序无法启动？**
A: 请确保您的系统是Windows 10或更高版本

**Q: 图表无法显示？**
A: 请检查数据文件格式是否正确

**Q: 复制图表失败？**
A: 可以使用"保存图表"功能保存到文件后手动复制

## 📞 技术支持
如有问题请联系开发团队
"""
    
    with open("使用说明.txt", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ 使用说明文件创建成功")

def main():
    """主函数"""
    print("🚀 开始构建Java标准化分析工具exe文件")
    print("=" * 50)
    
    # 检查主程序文件是否存在
    if not os.path.exists("Java标准化分析工具UI.py"):
        print("❌ 找不到主程序文件: Java标准化分析工具UI.py")
        return
    
    # 步骤1: 安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 步骤2: 安装依赖包
    if not install_requirements():
        return
    
    # 步骤3: 创建图标
    create_icon()
    
    # 步骤4: 构建exe
    if not build_exe():
        return
    
    # 步骤5: 创建使用说明
    create_user_guide()
    
    # 步骤6: 清理构建文件
    clean_build_files()
    
    print("\n" + "=" * 50)
    print("🎉 构建完成！")
    print("📁 exe文件位置: dist/Java标准化分析工具.exe")
    print("📖 使用说明: 使用说明.txt")
    print("\n💡 提示：")
    print("- 可以将dist文件夹中的exe文件分发给用户")
    print("- 建议同时提供使用说明.txt文件")

if __name__ == "__main__":
    main()
