# Java标准化分析工具 - 打包分发说明

## 🎯 打包目标
将Python脚本打包为独立的exe文件，方便小白用户直接使用，无需安装Python环境。

## 📦 打包方法

### 方法一：使用自动打包脚本（推荐）
1. 运行 `build_exe.py`：
   ```bash
   python build_exe.py
   ```
   
2. 脚本会自动：
   - 安装PyInstaller
   - 安装所需依赖包
   - 创建图标文件
   - 构建exe文件
   - 生成使用说明

### 方法二：使用批处理文件
1. 双击运行 `简单打包.bat`
2. 等待打包完成

### 方法三：手动打包
1. 安装依赖：
   ```bash
   pip install pyinstaller matplotlib pillow pywin32 numpy
   ```

2. 执行打包命令：
   ```bash
   pyinstaller --onefile --windowed --name="Java标准化分析工具" "Java标准化分析工具UI.py"
   ```

## 📁 分发文件清单

打包完成后，需要分发给用户的文件：

### 必需文件
- `Java标准化分析工具.exe` - 主程序（位于dist文件夹）

### 可选文件
- `使用说明.txt` - 用户使用指南
- `示例数据文件/` - 示例数据文件夹（如果有）

## 💻 系统要求

### 用户系统要求
- **操作系统**：Windows 10 或更高版本
- **内存**：至少 4GB RAM
- **磁盘空间**：至少 100MB 可用空间

### 开发环境要求
- Python 3.8 或更高版本
- 已安装所有依赖包

## 🔧 打包优化选项

### 减小文件大小
```bash
pyinstaller --onefile --windowed --strip --optimize=2 "Java标准化分析工具UI.py"
```

### 包含额外文件
```bash
pyinstaller --onefile --windowed --add-data "config.ini;." "Java标准化分析工具UI.py"
```

### 自定义图标
```bash
pyinstaller --onefile --windowed --icon="icon.ico" "Java标准化分析工具UI.py"
```

## 📋 测试清单

打包完成后，请在不同环境中测试：

- [ ] 在没有Python的Windows机器上运行
- [ ] 测试所有四个分析模块
- [ ] 测试文件选择功能
- [ ] 测试图表生成和复制功能
- [ ] 测试错误处理

## 🚀 分发方式

### 内部分发
1. 将exe文件放在共享文件夹
2. 提供使用说明文档
3. 建立用户反馈渠道

### 外部分发
1. 创建安装包（可选）
2. 提供在线下载链接
3. 准备技术支持文档

## ⚠️ 注意事项

1. **杀毒软件**：某些杀毒软件可能误报，需要添加白名单
2. **文件路径**：避免中文路径可能导致的问题
3. **权限问题**：确保用户有足够权限运行程序
4. **依赖检查**：在目标机器上测试所有功能

## 🐛 常见问题

### 打包失败
- 检查Python版本和依赖包
- 尝试在虚拟环境中打包
- 检查文件路径中是否有特殊字符

### 运行失败
- 检查目标系统是否满足要求
- 查看是否有杀毒软件拦截
- 检查文件完整性

### 功能异常
- 确认所有依赖包都已正确打包
- 检查数据文件格式
- 查看错误日志

## 📞 技术支持

如遇到打包或分发问题，请提供：
1. 错误信息截图
2. 系统环境信息
3. 打包命令和参数
4. Python版本和依赖包版本
