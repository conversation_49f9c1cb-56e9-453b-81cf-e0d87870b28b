import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics
import matplotlib.font_manager as fm

def plot_memory_usage(file_path):
    times = []
    total_pss_list = []
    native_pss_list = []

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                if "TotalPss" in data and "NativePss" in data and "time" in data:
                    times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                    total_pss_list.append(float(data["TotalPss"]))
                    native_pss_list.append(float(data["NativePss"]))
            except Exception as e:
                print(f"跳过一行数据：{e}")

    if not times:
        print("没有有效的内存数据。")
        return

    avg_total = statistics.mean(total_pss_list)
    avg_native = statistics.mean(native_pss_list)

    # 设置中文字体
    zh_font = fm.FontProperties(fname=fm.findfont(fm.FontProperties(family='SimHei')))

    plt.figure(figsize=(12, 6))
    plt.plot(times, total_pss_list, label=f'TotalPss (MB)', color='blue', linestyle='-', marker='o')
    plt.plot(times, native_pss_list, label=f'NativePss (MB)', color='orange', linestyle='-', marker='o')

    # 平均线
    plt.axhline(avg_total, color='blue', linestyle='--', alpha=0.6, label=f'TotalPss 平均值: {avg_total:.2f} MB')
    plt.axhline(avg_native, color='orange', linestyle='--', alpha=0.6, label=f'NativePss 平均值: {avg_native:.2f} MB')

    plt.xlabel("时间", fontproperties=zh_font)
    plt.ylabel("内存使用量 (MB)", fontproperties=zh_font)
    plt.title("内存使用趋势图", fontproperties=zh_font)
    plt.legend(prop=zh_font)
    plt.grid(True)
    plt.tight_layout()
    plt.gcf().autofmt_xdate()
    plt.show()

# 使用方法：
file_path = r"G:\22998移为SDM450\稳定性测试结果\DMS+ADAS\ADAS_DMS_STABILITY_RESULT_2.1.003.1103.130_20250616_113119\stability_resource_ADAS_DMS_2.1.003.1103.130.txt"
plot_memory_usage(file_path)
