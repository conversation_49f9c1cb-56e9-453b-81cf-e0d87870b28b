import json
import statistics
from collections import defaultdict

def analyze_cost_time_by_module(file_path):
    module_cost_times = defaultdict(list)

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                module = data.get("module")
                cost_time = data.get("costTime")

                if module and isinstance(cost_time, (int, float)):
                    module_cost_times[module].append(cost_time)
            except Exception as e:
                print(f"跳过一行：{e}")

    if not module_cost_times:
        print("未找到任何 costTime 数据。")
        return

    print("----- 按模块统计 CostTime 性能 -----")
    for module, cost_times in module_cost_times.items():
        avg = statistics.mean(cost_times)
        max_val = max(cost_times)
        min_val = min(cost_times)
        print(f"\n模块：{module}")
        print(f"  样本数：{len(cost_times)}")
        print(f"  平均值：{avg:.2f} ms")
        print(f"  最大值：{max_val:.2f} ms")
        print(f"  最小值：{min_val:.2f} ms")
    print("------------------------------------")

# 使用方法
file_path = r"G:\22998移为SDM450\稳定性测试结果\DMS+ADAS\ADAS_DMS_STABILITY_RESULT_2.1.003.1103.130_20250616_113119\stability_detect_ADAS_DMS_2.1.003.1103.130.txt"
analyze_cost_time_by_module(file_path)
