import json
import matplotlib.pyplot as plt
from datetime import datetime
import statistics
import matplotlib.font_manager as fm

def plot_cpu_usage(file_path):
    times = []
    cpu_usages = []

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                if "CpuUsage" in data and "time" in data:
                    times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                    cpu_usages.append(float(data["CpuUsage"]))
            except Exception as e:
                print(f"跳过一行数据：{e}")

    if not times:
        print("没有有效的 CPU 数据。")
        return

    avg_cpu = statistics.mean(cpu_usages)

    plt.figure(figsize=(12, 6))
    plt.plot(times, cpu_usages, color='red', label='CPU 使用率 (%)', marker='o')
    plt.axhline(avg_cpu, color='red', linestyle='--', alpha=0.6, label=f'平均值: {avg_cpu:.2f}%')

    plt.xlabel("时间", fontproperties='SimHei')
    plt.ylabel("CPU 使用率 (%)", fontproperties='SimHei')
    plt.title("CPU 使用率随时间变化", fontproperties='SimHei')
    plt.legend(prop=fm.FontProperties(fname=fm.findfont(fm.FontProperties(family='SimHei'))))
    plt.grid(True)
    plt.tight_layout()
    plt.gcf().autofmt_xdate()
    plt.ylim(0, 100)
    plt.show()

# 使用方法：
file_path = r"G:\22998移为SDM450\稳定性测试结果\DMS+ADAS\ADAS_DMS_STABILITY_RESULT_2.1.003.1103.130_20250616_113119\stability_resource_ADAS_DMS_2.1.003.1103.130.txt"
plot_cpu_usage(file_path)
