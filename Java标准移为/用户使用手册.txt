===============================================
    Java标准化分析工具 - 用户使用手册
===============================================

📋 工具简介
-----------
本工具是一个集成化的数据分析平台，包含四个主要分析模块：
• CPU使用率分析
• 内存使用分析  
• 性能耗时分析
• 模型加密检测

🚀 快速开始
-----------
1. 双击"Java标准化分析工具.exe"启动程序
2. 选择需要的分析模块（点击对应标签页）
3. 点击"浏览"按钮选择数据文件
4. 点击"分析"按钮开始分析
5. 查看分析结果和图表

📊 各模块详细说明
-----------------

【CPU分析模块】
• 功能：分析CPU使用率随时间的变化趋势
• 数据格式：JSON格式，包含time和CpuUsage字段
• 示例：{"time": "2024-01-01 10:00:00.000", "CpuUsage": 25.5}
• 输出：时间序列图表 + 统计信息（平均值、最大值、最小值）

【内存分析模块】
• 功能：分析内存使用量的变化趋势
• 数据格式：JSON格式，包含time、TotalPss、NativePss字段
• 示例：{"time": "2024-01-01 10:00:00.000", "TotalPss": 150.2, "NativePss": 80.1}
• 输出：双线趋势图 + 内存使用统计

【性能分析模块】
• 功能：按模块统计性能耗时数据
• 数据格式：JSON格式，包含module和costTime字段
• 示例：{"module": "FaceDetection", "costTime": 15.5}
• 输出：详细的性能统计表格

【模型加密分析模块】
• 功能：检测模型算法的加密情况
• 数据格式：日志文件，包含特定关键字
• 关键字："model type is" 和 "[CRYPT_INFO]"
• 输出：加密检测报告和安全评估结果

🖼️ 图表操作
-----------
【复制图表】
• 方法1：点击"复制图表"按钮
• 方法2：在图表上右键选择"复制图表"
• 复制后可直接粘贴到Word、PowerPoint等应用

【保存图表】
• 在图表上右键选择"保存图表"
• 支持PNG、PDF、SVG、JPEG等格式
• 可选择保存位置和文件名

📁 数据文件准备
---------------
【文件格式要求】
• CPU/内存/性能分析：每行一个JSON对象
• 模型加密分析：标准日志文件格式
• 文件编码：UTF-8（推荐）或GBK

【示例数据文件】
CPU数据文件内容：
{"time": "2024-01-01 10:00:00.000", "CpuUsage": 25.5}
{"time": "2024-01-01 10:00:01.000", "CpuUsage": 28.3}
{"time": "2024-01-01 10:00:02.000", "CpuUsage": 22.1}

内存数据文件内容：
{"time": "2024-01-01 10:00:00.000", "TotalPss": 150.2, "NativePss": 80.1}
{"time": "2024-01-01 10:00:01.000", "TotalPss": 152.5, "NativePss": 81.3}

性能数据文件内容：
{"module": "FaceDetection", "costTime": 15.5}
{"module": "EyeTracking", "costTime": 8.2}
{"module": "FaceDetection", "costTime": 16.1}

⚠️ 注意事项
-----------
• 确保数据文件格式正确，否则可能无法解析
• 大文件分析可能需要较长时间，请耐心等待
• 建议在分析前备份原始数据文件
• 如遇到问题，可查看程序提示的错误信息

❓ 常见问题解答
---------------
Q: 程序无法启动？
A: 请确保系统是Windows 10或更高版本，如仍有问题请联系技术支持

Q: 提示"没有有效数据"？
A: 请检查数据文件格式是否正确，参考上述示例格式

Q: 图表显示异常？
A: 可能是数据量过大或格式问题，建议检查数据文件

Q: 复制图表失败？
A: 可以使用"保存图表"功能保存到文件后手动复制

Q: 分析速度很慢？
A: 大数据文件需要较长处理时间，建议分批处理或使用更高配置的电脑

🔧 系统要求
-----------
• 操作系统：Windows 10 或更高版本
• 内存：建议4GB以上
• 磁盘空间：至少100MB可用空间
• 显示器：建议1920x1080分辨率

📞 技术支持
-----------
如遇到使用问题，请提供：
1. 错误信息截图
2. 使用的数据文件示例
3. 操作步骤描述
4. 系统环境信息

===============================================
版本信息：v1.0
更新日期：2024年12月
===============================================
