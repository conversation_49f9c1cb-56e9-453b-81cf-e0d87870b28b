@echo off
chcp 65001
echo 🚀 Java标准化分析工具 - 简单打包脚本
echo =======================================

echo.
echo 📦 步骤1: 安装PyInstaller...
pip install pyinstaller

echo.
echo 📦 步骤2: 安装依赖包...
pip install matplotlib pillow pywin32 numpy

echo.
echo 📦 步骤3: 开始打包...
pyinstaller --onefile --windowed --name="Java标准化分析工具" "Java标准化分析工具UI.py"

echo.
echo 📦 步骤4: 清理临时文件...
rmdir /s /q build
del "Java标准化分析工具.spec"

echo.
echo 🎉 打包完成！
echo 📁 exe文件位置: dist\Java标准化分析工具.exe
echo.
echo 💡 提示：可以将dist文件夹中的exe文件分发给用户使用
echo.
pause
