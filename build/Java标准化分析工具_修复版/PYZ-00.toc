('E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_修复版\\PYZ-00.pyz',
 [('PIL', 'f:\\python\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'f:\\python\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'f:\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'f:\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'f:\\python\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'f:\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'f:\\python\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'f:\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'f:\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'f:\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'f:\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'f:\\python\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'f:\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'f:\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'f:\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'f:\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'f:\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'f:\\python\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'f:\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'f:\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'f:\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'f:\\python\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'f:\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'f:\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'f:\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'f:\\python\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util', 'f:\\python\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'f:\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'f:\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'f:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'f:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'f:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('_dummy_thread', 'f:\\python\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('_py_abc', 'f:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'f:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'f:\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'f:\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_strptime', 'f:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'f:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'f:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'f:\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'f:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'f:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'f:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'f:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'f:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'f:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'f:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'f:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'f:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'f:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'f:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'f:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'f:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'f:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'f:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'f:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'f:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'f:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.streams', 'f:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'f:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.tasks', 'f:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.transports', 'f:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'f:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'f:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'f:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'f:\\python\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'f:\\python\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'f:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'f:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'f:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'f:\\python\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'f:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'f:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'f:\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'f:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'f:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'f:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'f:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'f:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'f:\\python\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'f:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'f:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'f:\\python\\lib\\copy.py', 'PYMODULE'),
  ('ctypes', 'f:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'f:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'f:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'f:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'f:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'f:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'f:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'f:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'f:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('cycler', 'f:\\python\\lib\\site-packages\\cycler.py', 'PYMODULE'),
  ('dataclasses', 'f:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'f:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'f:\\python\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'f:\\python\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'f:\\python\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'f:\\python\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'f:\\python\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'f:\\python\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'f:\\python\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'f:\\python\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'f:\\python\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'f:\\python\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'f:\\python\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'f:\\python\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'f:\\python\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'f:\\python\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'f:\\python\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'f:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'f:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'f:\\python\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'f:\\python\\lib\\doctest.py', 'PYMODULE'),
  ('dummy_threading', 'f:\\python\\lib\\dummy_threading.py', 'PYMODULE'),
  ('email', 'f:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'f:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'f:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'f:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'f:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'f:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'f:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'f:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'f:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'f:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'f:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'f:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'f:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'f:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'f:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'f:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'f:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'f:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'f:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'f:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('fractions', 'f:\\python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'f:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'f:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'f:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'f:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'f:\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'f:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'f:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'f:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'f:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'f:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'f:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'f:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'f:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'f:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('imp', 'f:\\python\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'f:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'f:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'f:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'f:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'f:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'f:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'f:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'f:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'f:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'f:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'f:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'f:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('kiwisolver',
   'f:\\python\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'f:\\python\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging', 'f:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'f:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('matplotlib',
   'f:\\python\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api',
   'f:\\python\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'f:\\python\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'f:\\python\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'f:\\python\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'f:\\python\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'f:\\python\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'f:\\python\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'f:\\python\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'f:\\python\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'f:\\python\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'f:\\python\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'f:\\python\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'f:\\python\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._version',
   'f:\\python\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\afm.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'f:\\python\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'f:\\python\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'f:\\python\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'f:\\python\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'f:\\python\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._subplots',
   'f:\\python\\lib\\site-packages\\matplotlib\\axes\\_subplots.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'f:\\python\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'f:\\python\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'f:\\python\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'f:\\python\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'f:\\python\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'f:\\python\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'f:\\python\\lib\\site-packages\\matplotlib\\cbook\\__init__.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'f:\\python\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'f:\\python\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'f:\\python\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'f:\\python\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'f:\\python\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'f:\\python\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'f:\\python\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.docstring',
   'f:\\python\\lib\\site-packages\\matplotlib\\docstring.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'f:\\python\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'f:\\python\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'f:\\python\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.fontconfig_pattern',
   'f:\\python\\lib\\site-packages\\matplotlib\\fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'f:\\python\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'f:\\python\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'f:\\python\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'f:\\python\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'f:\\python\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'f:\\python\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'f:\\python\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'f:\\python\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'f:\\python\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'f:\\python\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'f:\\python\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'f:\\python\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'f:\\python\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'f:\\python\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'f:\\python\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'f:\\python\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'f:\\python\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'f:\\python\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'f:\\python\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'f:\\python\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'f:\\python\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'f:\\python\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'f:\\python\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'f:\\python\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'f:\\python\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'f:\\python\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'f:\\python\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'f:\\python\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'f:\\python\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'f:\\python\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.tight_bbox',
   'f:\\python\\lib\\site-packages\\matplotlib\\tight_bbox.py',
   'PYMODULE'),
  ('matplotlib.tight_layout',
   'f:\\python\\lib\\site-packages\\matplotlib\\tight_layout.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'f:\\python\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri.triangulation',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri.tricontour',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri.trifinder',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri.triinterpolate',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri.tripcolor',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri.triplot',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\triplot.py',
   'PYMODULE'),
  ('matplotlib.tri.trirefine',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri.tritools',
   'f:\\python\\lib\\site-packages\\matplotlib\\tri\\tritools.py',
   'PYMODULE'),
  ('matplotlib.units',
   'f:\\python\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'f:\\python\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes', 'f:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'f:\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'f:\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'f:\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'f:\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'f:\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'f:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'f:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'f:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'f:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'f:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'f:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'f:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'f:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'f:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'f:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'f:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'f:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'f:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'f:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'f:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'f:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'f:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'f:\\python\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'f:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'f:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'f:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'f:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'f:\\python\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'f:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'f:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'f:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'f:\\python\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'f:\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'f:\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'f:\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'f:\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._version',
   'f:\\python\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.compat',
   'f:\\python\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'f:\\python\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'f:\\python\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'f:\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'f:\\python\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'f:\\python\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'f:\\python\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'f:\\python\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'f:\\python\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'f:\\python\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'f:\\python\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'f:\\python\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'f:\\python\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'f:\\python\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'f:\\python\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'f:\\python\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'f:\\python\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'f:\\python\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'f:\\python\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'f:\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'f:\\python\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'f:\\python\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'f:\\python\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'f:\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'f:\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'f:\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'f:\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'f:\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'f:\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.version',
   'f:\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'f:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'f:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'f:\\python\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'f:\\python\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'f:\\python\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'f:\\python\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'f:\\python\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'f:\\python\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'f:\\python\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'f:\\python\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'f:\\python\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'f:\\python\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'f:\\python\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'f:\\python\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'f:\\python\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'f:\\python\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pdb', 'f:\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'f:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'f:\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'f:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'f:\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'f:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'f:\\python\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'f:\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'f:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'f:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'f:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'f:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'f:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyparsing',
   'f:\\python\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'f:\\python\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'f:\\python\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'f:\\python\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'f:\\python\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'f:\\python\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'f:\\python\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'f:\\python\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'f:\\python\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'f:\\python\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'f:\\python\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('queue', 'f:\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'f:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'f:\\python\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'f:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('selectors', 'f:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'f:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'f:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'f:\\python\\lib\\signal.py', 'PYMODULE'),
  ('six', 'f:\\python\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'f:\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'f:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'f:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'f:\\python\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'f:\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'f:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'f:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'f:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'f:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'f:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'f:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'f:\\python\\lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'f:\\python\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'f:\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'f:\\python\\lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'f:\\python\\lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'f:\\python\\lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.font', 'f:\\python\\lib\\tkinter\\font.py', 'PYMODULE'),
  ('tkinter.messagebox', 'f:\\python\\lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.scrolledtext',
   'f:\\python\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'f:\\python\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'f:\\python\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tracemalloc', 'f:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'f:\\python\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'f:\\python\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'f:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'f:\\python\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.case', 'f:\\python\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'f:\\python\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'f:\\python\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result', 'f:\\python\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'f:\\python\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'f:\\python\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'f:\\python\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'f:\\python\\lib\\unittest\\util.py', 'PYMODULE'),
  ('uu', 'f:\\python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'f:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'f:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'f:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'f:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'f:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'f:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'f:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'f:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'f:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'f:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'f:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'f:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'f:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'f:\\python\\lib\\zipfile.py', 'PYMODULE')])
