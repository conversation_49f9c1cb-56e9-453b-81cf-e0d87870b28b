('E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\Java标准化分析工具_新版.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'f:\\python\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'f:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('Java标准化分析工具UI',
   'E:\\PyCharmProjects\\pythonProject\\Java标准移为\\Java标准化分析工具UI.py',
   'PYSOURCE'),
  ('python37.dll', 'f:\\python\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'f:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'f:\\python\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('_ctypes.pyd', 'f:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'f:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'f:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'f:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'f:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'f:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'f:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_contextvars.pyd', 'f:\\python\\DLLs\\_contextvars.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'f:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'f:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'f:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_queue.pyd', 'f:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'f:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'f:\\python\\lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'f:\\python\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\kiwisolver\\_cext.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'f:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'f:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_c_internal_utils.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'f:\\python\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_tri.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_qhull.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_contour.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_contour.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_image.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\_path.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp37-win_amd64.pyd',
   'f:\\python\\lib\\site-packages\\matplotlib\\ft2font.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'f:\\python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('libssl-1_1-x64.dll', 'f:\\python\\DLLs\\libssl-1_1-x64.dll', 'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'f:\\python\\DLLs\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes37.dll',
   'f:\\python\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Windows\\system32\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('tk86t.dll', 'f:\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'f:\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('base_library.zip',
   'E:\\PyCharmProjects\\pythonProject\\build\\Java标准化分析工具_新版\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\msgs\\kw.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Iran', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Israel', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Israel', 'DATA'),
  ('tcl\\msgs\\de_be.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\de_be.msg', 'DATA'),
  ('tk\\optMenu.tcl', 'f:\\python\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tcl\\msgs\\kw_gb.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg', 'DATA'),
  ('tcl\\tzdata\\Universal',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\msgs\\bg.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('tcl\\auto.tcl', 'f:\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\msgs\\en_be.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_be.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\ROC', 'f:\\python\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tk\\menu.tcl', 'f:\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\clock.tcl', 'f:\\python\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\be.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('tcl\\parray.tcl', 'f:\\python\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg', 'DATA'),
  ('tk\\msgs\\fr.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tk\\msgs\\hu.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('tk\\msgs\\el.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tk\\ttk\\scale.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\scale.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tk\\scale.tcl', 'f:\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tk\\msgs\\de.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('tcl\\msgs\\ga.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tk\\palette.tcl', 'f:\\python\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tk\\images\\README', 'f:\\python\\tcl\\tk8.6\\images\\README', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('tcl\\msgs\\de.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Turkey', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Turkey', 'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl8\\8.6\\http-2.8.12.tm',
   'f:\\python\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.8.12.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-ticks.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\GMT0', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\MST', 'f:\\python\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\Eire', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('tcl\\msgs\\en_sg.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg', 'DATA'),
  ('tk\\tclIndex', 'f:\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Navajo', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Navajo', 'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-poster.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Libya', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Libya', 'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'f:\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tk\\msgs\\en_gb.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\en_gb.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg', 'DATA'),
  ('tcl\\msgs\\es_hn.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg', 'DATA'),
  ('tcl\\msgs\\hi.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('tcl\\tzdata\\MET', 'f:\\python\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('tcl\\tzdata\\UCT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tk\\fontchooser.tcl', 'f:\\python\\tcl\\tk8.6\\fontchooser.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg', 'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tk\\iconlist.tcl', 'f:\\python\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tk\\ttk\\fonts.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\fonts.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\msgs\\de_at.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\de_at.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\msgs\\th.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('tk\\mkpsenc.tcl', 'f:\\python\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\word.tcl', 'f:\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\msgs\\sw.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('tcl\\tzdata\\UTC', 'f:\\python\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\msgs\\eo.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tk\\ttk\\cursors.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\cursors.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'f:\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg', 'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\msgs\\af.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\GB', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('tcl\\msgs\\bn.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tk\\text.tcl', 'f:\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl8\\8.5\\tcltest-2.4.1.tm',
   'f:\\python\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.4.1.tm',
   'DATA'),
  ('tcl\\msgs\\el.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\msgs\\sq.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-pastel.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\fi.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('tcl\\msgs\\ms.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tk\\tk.tcl', 'f:\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Japan', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Japan', 'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg', 'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT', 'DATA'),
  ('tcl\\tzdata\\ROK', 'f:\\python\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'f:\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\msgs\\cs.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\msgs\\sh.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\msgs\\kl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg', 'DATA'),
  ('tcl\\msgs\\id_id.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\id_id.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\msgs\\ko.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('tcl\\msgs\\gl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\msgs\\en_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_in.msg', 'DATA'),
  ('tcl\\msgs\\uk.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tk\\safetk.tcl', 'f:\\python\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tk\\scrlbar.tcl', 'f:\\python\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\msgs\\mk.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\safe.tcl', 'f:\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tk\\msgbox.tcl', 'f:\\python\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg', 'DATA'),
  ('tk\\msgs\\es.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tclIndex', 'f:\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\msgs\\et.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\msgs\\ro.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\msgs\\fo.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Zulu', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\PRC', 'f:\\python\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'f:\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-bright.mplstyle',
   'DATA'),
  ('tcl\\msgs\\sl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\package.tcl', 'f:\\python\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-white.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tk\\spinbox.tcl', 'f:\\python\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tk\\dialog.tcl', 'f:\\python\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\gv.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\msgs\\lv.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\msgs\\nb.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tk\\msgs\\sv.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\tzdata\\GMT+0', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GMT+0', 'DATA'),
  ('tcl\\tzdata\\HST', 'f:\\python\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\msgs\\lt.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\GMT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\msgs\\ar.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('tcl\\msgs\\mt.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\WET', 'f:\\python\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Iceland', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Iceland', 'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg', 'DATA'),
  ('tk\\images\\logo64.gif',
   'f:\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tk\\tkfbox.tcl', 'f:\\python\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'f:\\python\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT', 'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tk\\bgerror.tcl', 'f:\\python\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'f:\\python\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\msgs\\te.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('tcl\\msgs\\es_pa.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-whitegrid.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-deep.mplstyle',
   'DATA'),
  ('tk\\icons.tcl', 'f:\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\msgs\\ru.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\tzdata\\PST8PDT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT', 'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\msgs\\id.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('tcl\\tzdata\\Poland', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Poland', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark-palette.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tk\\msgs\\pt.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'f:\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tk\\ttk\\entry.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\entry.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\msgs\\fa.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg', 'DATA'),
  ('tcl\\msgs\\is.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-darkgrid.mplstyle',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\msgs\\hr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Jamaica', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Jamaica', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'f:\\python\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\msgs\\nn.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'DATA'),
  ('tk\\megawidget.tcl', 'f:\\python\\tcl\\tk8.6\\megawidget.tcl', 'DATA'),
  ('tcl\\msgs\\ar_sy.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'f:\\python\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\msgs\\es_co.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_co.msg', 'DATA'),
  ('tcl\\msgs\\zh.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('tk\\msgs\\ru.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tk\\focus.tcl', 'f:\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl', 'DATA'),
  ('tcl\\msgs\\ru_ua.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg', 'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg', 'DATA'),
  ('tcl\\tzdata\\EST', 'f:\\python\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-muted.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\msgs\\es.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\msgs\\nl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('tk\\msgs\\en.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'f:\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('tk\\msgs\\nl.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\CET', 'f:\\python\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg', 'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\msgs\\mr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\msgs\\tr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tk\\comdlg.tcl', 'f:\\python\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tk\\images\\logo.eps', 'f:\\python\\tcl\\tk8.6\\images\\logo.eps', 'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('tcl\\tzdata\\Cuba', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\msgs\\en_za.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_za.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tk\\obsolete.tcl', 'f:\\python\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('tcl\\msgs\\ar_lb.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg', 'DATA'),
  ('tcl\\msgs\\en_ca.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\msgs\\fr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tk\\clrpick.tcl', 'f:\\python\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tk\\choosedir.tcl', 'f:\\python\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('tcl\\msgs\\en_ph.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\msgs\\it.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('tcl\\msgs\\sk.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tk\\ttk\\ttk.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tk\\unsupported.tcl', 'f:\\python\\tcl\\tk8.6\\unsupported.tcl', 'DATA'),
  ('tcl\\tzdata\\GB-Eire', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg', 'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tk\\pkgIndex.tcl', 'f:\\python\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\msgs\\kok.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\kok.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\msgs\\pt.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tk\\panedwindow.tcl', 'f:\\python\\tcl\\tk8.6\\panedwindow.tcl', 'DATA'),
  ('tcl\\tzdata\\EST5EDT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT', 'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\msgs\\ja.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tk\\ttk\\button.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\button.tcl', 'DATA'),
  ('tk\\msgs\\pl.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tk\\console.tcl', 'f:\\python\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg', 'DATA'),
  ('tk\\license.terms', 'f:\\python\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\msgs\\es_py.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_py.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\msgs\\ta.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-paper.mplstyle',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg', 'DATA'),
  ('tcl\\msgs\\he.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Egypt', 'f:\\python\\tcl\\tcl8.6\\tzdata\\Egypt', 'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_128.ppm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-notebook.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\msgs\\te_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\te_in.msg', 'DATA'),
  ('tk\\ttk\\progress.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\init.tcl', 'f:\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'f:\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg', 'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tk\\listbox.tcl', 'f:\\python\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\W-SU', 'f:\\python\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('tcl\\msgs\\zh_cn.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg', 'DATA'),
  ('tk\\images\\logoMed.gif',
   'f:\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('tcl\\msgs\\vi.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('tcl\\msgs\\ca.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\msgs\\pl.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('tcl\\msgs\\af_za.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\af_za.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-talk.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tk\\entry.tcl', 'f:\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\msgs\\eu.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\msgs\\es_do.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_do.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\msgs\\hu.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\history.tcl', 'f:\\python\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg', 'DATA'),
  ('tk\\images\\tai-ku.gif',
   'f:\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg', 'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\msgs\\da.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\msgs\\sv.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-dark.mplstyle',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT', 'f:\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT', 'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tk\\button.tcl', 'f:\\python\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tcl\\tm.tcl', 'f:\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg', 'DATA'),
  ('tk\\xmfbox.tcl', 'f:\\python\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tk\\ttk\\utils.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\utils.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tk\\msgs\\cs.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tk\\msgs\\da.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl', 'f:\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'f:\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tk\\tearoff.tcl', 'f:\\python\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tk\\msgs\\eo.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('tcl\\tzdata\\GMT-0', 'f:\\python\\tcl\\tcl8.6\\tzdata\\GMT-0', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('tcl\\msgs\\sr.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'f:\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\EET', 'f:\\python\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('tcl\\msgs\\mr_in.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg', 'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'f:\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tk\\msgs\\it.msg', 'f:\\python\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'f:\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'f:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\msgs\\en_au.msg', 'f:\\python\\tcl\\tcl8.6\\msgs\\en_au.msg', 'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
