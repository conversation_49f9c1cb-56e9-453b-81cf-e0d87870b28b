import re

# 测试样本行
test_lines = [
    "04-14 18:10:42.351  9015  9094 V CASCModelManager: [113:onAnalysisHeader] processType is 0, model type is [0-0/7], size:{88, 5408}",
    "04-14 18:10:42.355  9015  9094 I ANPU_LOG : [CRYPT_INFO] input_data MD5 [cf8c28e9936a7f8804fbf1ba792c0fa4], size [42672], decrypt time [0.2310 ms]"
]

print("=== 测试正则表达式匹配 ===")

for i, line in enumerate(test_lines, 1):
    print(f"\n测试行 {i}: {line}")
    
    if "model type is" in line:
        model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
        if model_match:
            size1 = int(model_match.group(1))
            size2 = int(model_match.group(2))
            print(f"  ✓ 模型匹配成功: size1={size1}, size2={size2} (使用第二列: {size2})")
        else:
            print("  ✗ 模型匹配失败")
    
    elif "[CRYPT_INFO]" in line:
        crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
        if crypt_match:
            size = int(crypt_match.group(1))
            print(f"  ✓ 加密匹配成功: size={size}")
        else:
            print("  ✗ 加密匹配失败")

print("\n=== 开始分析真实日志文件 ===")

# 分析真实日志文件
model_info = []
crypt_info = []

try:
    with open("test_log.log", 'r', encoding='utf-8', errors='ignore') as f:
        line_number = 0
        for line in f:
            line_number += 1
            line = line.strip()
            
            if "model type is" in line:
                model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                if model_match:
                    size = int(model_match.group(2))  # 第二列
                    model_info.append((size, line_number))
                    print(f"模型 [行{line_number}]: {size}")
            
            elif "[CRYPT_INFO]" in line:
                crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                if crypt_match:
                    size = int(crypt_match.group(1))
                    crypt_info.append((size, line_number))
                    print(f"加密 [行{line_number}]: {size}")

    print(f"\n总计: {len(model_info)} 个模型, {len(crypt_info)} 个加密")
    
    # 排序
    model_info.sort()
    crypt_info.sort()
    
    print("\n模型算法 (排序后):")
    for i, (size, line_num) in enumerate(model_info, 1):
        print(f"{i:2d}. {size:8d} Bytes")
    
    print("\n加密算法 (排序后):")
    for i, (size, line_num) in enumerate(crypt_info, 1):
        print(f"{i:2d}. {size:8d} Bytes")
    
    # 匹配分析
    model_sizes = [info[0] for info in model_info]
    crypt_sizes = [info[0] for info in crypt_info]
    
    unmatched = []
    used_crypt = set()
    
    print("\n匹配过程:")
    for m_size in model_sizes:
        matched = False
        for j, c_size in enumerate(crypt_sizes):
            if j in used_crypt:
                continue
            if m_size == c_size:
                used_crypt.add(j)
                matched = True
                print(f"✓ {m_size} = {c_size}")
                break
            elif m_size == c_size + 32:
                used_crypt.add(j)
                matched = True
                print(f"✓ {m_size} = {c_size}+32 (NPU)")
                break
        
        if not matched:
            unmatched.append(m_size)
            print(f"✗ {m_size} 未匹配")
    
    print(f"\n结果A: {len(unmatched)} 个未加密算法")
    if unmatched:
        kb_threshold = 10 * 1024
        for size in unmatched:
            kb = size / 1024
            status = "符合" if size < kb_threshold else "不符合"
            print(f"  {size} Bytes ({kb:.2f} KB) - {status}")
    
except Exception as e:
    print(f"错误: {e}")
