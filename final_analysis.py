#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def main():
    log_path = "test_log.log"
    output_file = "result.txt"
    
    with open(output_file, 'w', encoding='utf-8') as out:
        def print_to_file(*args, **kwargs):
            print(*args, file=out, **kwargs)
            print(*args, **kwargs)  # 同时输出到控制台
        
        print_to_file("=== DMS算法加密检测工具 ===")
        print_to_file(f"分析日志文件: {log_path}")
        
        if not os.path.exists(log_path):
            print_to_file(f"错误：日志文件不存在 - {log_path}")
            return
        
        model_info = []
        crypt_info = []
        
        # 解析日志文件
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_number = 0
            for line in f:
                line_number += 1
                line = line.strip()
                
                # 查找包含 "model type is" 的行
                if "model type is" in line:
                    # 匹配格式: model type is [x-y/z], size:{数字1, 数字2}
                    # 提取第二列数字
                    model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                    if model_match:
                        size = int(model_match.group(2))  # 第二列
                        model_info.append((size, line, line_number))
                        print_to_file(f"找到模型信息 [行{line_number}]: size={size}")
                
                # 查找包含 "[CRYPT_INFO]" 的行
                elif "[CRYPT_INFO]" in line:
                    # 匹配格式: [CRYPT_INFO] input_data MD5 [...], size [数字], decrypt time [...]
                    crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                    if crypt_match:
                        size = int(crypt_match.group(1))
                        crypt_info.append((size, line, line_number))
                        print_to_file(f"找到加密信息 [行{line_number}]: size={size}")
        
        print_to_file(f"\n解析完成:")
        print_to_file(f"- 找到 {len(model_info)} 个模型算法")
        print_to_file(f"- 找到 {len(crypt_info)} 个加密算法")
        
        # 按大小排序
        model_info.sort(key=lambda x: x[0])
        crypt_info.sort(key=lambda x: x[0])
        
        print_to_file("\n=== 排序后的模型算法 (按size从小到大) ===")
        for i, (size, line, line_num) in enumerate(model_info, 1):
            print_to_file(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")
        
        print_to_file("\n=== 排序后的加密算法 (按size从小到大) ===")
        for i, (size, line, line_num) in enumerate(crypt_info, 1):
            print_to_file(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")
        
        # 匹配算法
        model_sizes = [info[0] for info in model_info]
        crypt_sizes = [info[0] for info in crypt_info]
        
        unmatched_models = []
        used_crypt_indexes = set()
        matched_pairs = []
        
        print_to_file("\n=== 算法匹配过程 ===")
        
        for i, m_size in enumerate(model_sizes):
            matched = False
            for j, c_size in enumerate(crypt_sizes):
                if j in used_crypt_indexes:
                    continue
                
                # 检查两种匹配情况：
                # 1. 普通模型：m_size == c_size
                # 2. NPU模型：m_size == c_size + 32
                if m_size == c_size:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "普通模型"))
                    print_to_file(f"✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型)")
                    break
                elif m_size == c_size + 32:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "NPU模型"))
                    print_to_file(f"✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型)")
                    break
            
            if not matched:
                unmatched_models.append(m_size)
                print_to_file(f"✗ 未匹配: 模型{m_size} (未加密)")
        
        print_to_file(f"\n=== 匹配结果统计 ===")
        print_to_file(f"总模型数量: {len(model_sizes)}")
        print_to_file(f"总加密数量: {len(crypt_sizes)}")
        print_to_file(f"成功匹配: {len(matched_pairs)}")
        print_to_file(f"未加密算法: {len(unmatched_models)}")
        
        # 分析未加密算法
        if not unmatched_models:
            print_to_file("\n🎉 结果A：所有算法都已加密！")
            return True
        
        print_to_file(f"\n=== 未加密算法分析 ===")
        print_to_file("以下算法未加密（单位：Byte）：")
        
        all_under_10kb = True
        kb_threshold = 10 * 1024  # 10KB = 10240 Bytes
        
        for i, size in enumerate(unmatched_models, 1):
            size_kb = size / 1024
            status = "✓" if size < kb_threshold else "✗"
            print_to_file(f"{i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) {status}")
            
            if size >= kb_threshold:
                all_under_10kb = False
        
        print_to_file(f"\n{'='*50}")
        print_to_file(f"结果A分析:")
        print_to_file(f"- 未加密算法数量: {len(unmatched_models)}")
        print_to_file(f"- 10KB阈值: {kb_threshold} Bytes")
        
        if all_under_10kb:
            print_to_file("✅ 结果A：所有未加密的算法都小于10KB - 符合要求")
        else:
            print_to_file("❌ 结果A：存在大于等于10KB的未加密算法 - 不符合要求")
            large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
            print_to_file(f"   大于等于10KB的算法: {len(large_algorithms)} 个")
            for size in large_algorithms:
                print_to_file(f"   - {size} Bytes ({size/1024:.2f} KB)")
        
        return all_under_10kb

if __name__ == "__main__":
    main()
