import re

# 快速测试
print("开始分析...")

model_count = 0
crypt_count = 0

with open("test_log.log", 'r', encoding='utf-8', errors='ignore') as f:
    for line_num, line in enumerate(f, 1):
        if "model type is" in line:
            if re.search(r"size:\{(\d+),\s*(\d+)\}", line):
                model_count += 1
                if model_count <= 5:  # 只显示前5个
                    print(f"模型 {model_count}: 行 {line_num}")
        
        elif "[CRYPT_INFO]" in line:
            if re.search(r"size\s*\[(\d+)\]", line):
                crypt_count += 1
                if crypt_count <= 5:  # 只显示前5个
                    print(f"加密 {crypt_count}: 行 {line_num}")

print(f"总计: {model_count} 个模型, {crypt_count} 个加密")

# 写入文件
with open("quick_result.txt", "w", encoding="utf-8") as f:
    f.write(f"模型数量: {model_count}\n")
    f.write(f"加密数量: {crypt_count}\n")

print("完成！结果已写入 quick_result.txt")
