#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 重定向输出到文件
with open('analysis_result.txt', 'w', encoding='utf-8') as f:
    # 保存原始的stdout
    original_stdout = sys.stdout
    # 重定向stdout到文件
    sys.stdout = f
    
    try:
        # 导入并运行分析脚本
        exec(open('安全测试脚本.py', encoding='utf-8').read())
    except Exception as e:
        print(f"执行出错: {e}")
    finally:
        # 恢复原始的stdout
        sys.stdout = original_stdout

print("分析完成，结果已保存到 analysis_result.txt")
