#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试...")

# 测试基本功能
import re
import os

log_content = """[2024-01-15 10:30:15] System initialization started
[2024-01-15 10:30:16] Loading DMS module...
model type is face_detection, version 1.2.3, size:{1024, 8192}
[2024-01-15 10:30:17] Model loaded successfully
[CRYPT_INFO] Algorithm: face_detection, encrypted: true, size [8192]
[2024-01-15 10:30:18] Loading next model...
model type is eye_tracking, version 2.1.0, size:{2048, 15360}
[2024-01-15 10:30:19] Model loaded successfully
[CRYPT_INFO] Algorithm: eye_tracking, encrypted: true, size [15328]
[2024-01-15 10:30:20] Loading next model...
model type is drowsiness_detection, version 1.5.2, size:{512, 4096}
[2024-01-15 10:30:21] Model loaded successfully
[2024-01-15 10:30:22] Loading next model...
model type is distraction_monitor, version 3.0.1, size:{256, 2048}
[2024-01-15 10:30:23] Model loaded successfully
[CRYPT_INFO] Algorithm: distraction_monitor, encrypted: true, size [2048]
[2024-01-15 10:30:24] Loading next model...
model type is head_pose_estimation, version 1.8.0, size:{128, 1024}
[2024-01-15 10:30:25] Model loaded successfully"""

print("=== DMS算法加密检测工具 ===")

model_info = []
crypt_info = []

lines = log_content.split('\n')
for line_number, line in enumerate(lines, 1):
    line = line.strip()
    
    # 查找 "model type is" 开头的行
    if line.startswith("model type is"):
        # 匹配 size:{数字,数字} 格式，提取第二列
        model_match = re.search(r"size:\{(\d+),\s*(\d+)\}", line)
        if model_match:
            size = int(model_match.group(2))  # 第二列
            model_info.append((size, line, line_number))
            print(f"找到模型信息 [行{line_number}]: size={size}")
    
    # 查找 "[CRYPT_INFO]" 开头的行
    elif line.startswith("[CRYPT_INFO]"):
        # 匹配 size [数字] 格式
        crypt_match = re.search(r"size\s*\[(\d+)\]", line)
        if crypt_match:
            size = int(crypt_match.group(1))
            crypt_info.append((size, line, line_number))
            print(f"找到加密信息 [行{line_number}]: size={size}")

print(f"\n解析完成:")
print(f"- 找到 {len(model_info)} 个模型算法")
print(f"- 找到 {len(crypt_info)} 个加密算法")

# 按大小排序
model_info.sort(key=lambda x: x[0])
crypt_info.sort(key=lambda x: x[0])

print("\n=== 排序后的模型算法 (按size从小到大) ===")
for i, (size, line, line_num) in enumerate(model_info, 1):
    print(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")

print("\n=== 排序后的加密算法 (按size从小到大) ===")
for i, (size, line, line_num) in enumerate(crypt_info, 1):
    print(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")

# 匹配算法
model_sizes = [info[0] for info in model_info]
crypt_sizes = [info[0] for info in crypt_info]

unmatched_models = []
used_crypt_indexes = set()

print("\n=== 算法匹配过程 ===")

for i, m_size in enumerate(model_sizes):
    matched = False
    for j, c_size in enumerate(crypt_sizes):
        if j in used_crypt_indexes:
            continue
        
        # 检查两种匹配情况：
        # 1. 普通模型：m_size == c_size
        # 2. NPU模型：m_size == c_size + 32
        if m_size == c_size:
            used_crypt_indexes.add(j)
            matched = True
            print(f"✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型)")
            break
        elif m_size == c_size + 32:
            used_crypt_indexes.add(j)
            matched = True
            print(f"✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型)")
            break
    
    if not matched:
        unmatched_models.append(m_size)
        print(f"✗ 未匹配: 模型{m_size} (未加密)")

print(f"\n=== 匹配结果统计 ===")
print(f"总模型数量: {len(model_sizes)}")
print(f"总加密数量: {len(crypt_sizes)}")
print(f"未加密算法: {len(unmatched_models)}")

# 分析未加密算法
if not unmatched_models:
    print("\n🎉 结果A：所有算法都已加密！")
else:
    print(f"\n=== 未加密算法分析 ===")
    print("以下算法未加密（单位：Byte）：")
    
    all_under_10kb = True
    kb_threshold = 10 * 1024  # 10KB = 10240 Bytes
    
    for i, size in enumerate(unmatched_models, 1):
        size_kb = size / 1024
        status = "✓" if size < kb_threshold else "✗"
        print(f"{i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) {status}")
        
        if size >= kb_threshold:
            all_under_10kb = False
    
    print(f"\n{'='*50}")
    print(f"结果A分析:")
    print(f"- 未加密算法数量: {len(unmatched_models)}")
    print(f"- 10KB阈值: {kb_threshold} Bytes")
    
    if all_under_10kb:
        print("✅ 结果A：所有未加密的算法都小于10KB - 符合要求")
    else:
        print("❌ 结果A：存在大于等于10KB的未加密算法 - 不符合要求")

print("\n测试完成！")
