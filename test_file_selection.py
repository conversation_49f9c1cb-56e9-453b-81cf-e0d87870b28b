import tkinter as tk
from tkinter import filedialog

def test_file_selection():
    """测试多文件选择功能"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    print("正在打开文件选择对话框...")
    print("提示：在文件对话框中，按住 Ctrl 键可以选择多个文件")
    
    file_paths = filedialog.askopenfilenames(
        title="选择多个文件（按住Ctrl键选择多个）",
        filetypes=[
            ("NV21 files", "*.NV21"),
            ("All files", "*.*")
        ]
    )
    
    root.destroy()
    
    print(f"选择的文件数量: {len(file_paths)}")
    for i, path in enumerate(file_paths, 1):
        print(f"{i}. {path}")
    
    return file_paths

if __name__ == "__main__":
    test_file_selection()
