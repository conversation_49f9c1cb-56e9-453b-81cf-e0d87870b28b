import re

# 测试样本
model_line = "04-14 18:10:42.351  9015  9094 V CASCModelManager: [113:onAnalysisHeader] processType is 0, model type is [0-0/7], size:{88, 5408}"
crypt_line = "04-14 18:10:42.355  9015  9094 I ANPU_LOG : [CRYPT_INFO] input_data MD5 [cf8c28e9936a7f8804fbf1ba792c0fa4], size [42672], decrypt time [0.2310 ms]"

print("测试正则表达式匹配:")
print(f"模型行: {model_line}")
print(f"加密行: {crypt_line}")

# 测试模型行匹配
if "model type is" in model_line:
    model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", model_line)
    if model_match:
        size1 = int(model_match.group(1))
        size2 = int(model_match.group(2))
        print(f"✓ 模型匹配成功: size1={size1}, size2={size2} (使用第二列: {size2})")
    else:
        print("✗ 模型匹配失败")

# 测试加密行匹配
if "[CRYPT_INFO]" in crypt_line:
    crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", crypt_line)
    if crypt_match:
        size = int(crypt_match.group(1))
        print(f"✓ 加密匹配成功: size={size}")
    else:
        print("✗ 加密匹配失败")

print("\n现在测试实际文件...")

model_count = 0
crypt_count = 0

with open("test_log.log", 'r', encoding='utf-8', errors='ignore') as f:
    for line_num, line in enumerate(f, 1):
        line = line.strip()
        
        if "model type is" in line:
            match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
            if match:
                size = int(match.group(2))
                model_count += 1
                if model_count <= 3:  # 只显示前3个
                    print(f"模型 {model_count}: 行{line_num}, size={size}")
        
        elif "[CRYPT_INFO]" in line:
            match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
            if match:
                size = int(match.group(1))
                crypt_count += 1
                if crypt_count <= 3:  # 只显示前3个
                    print(f"加密 {crypt_count}: 行{line_num}, size={size}")

print(f"\n总计: {model_count} 个模型, {crypt_count} 个加密")
