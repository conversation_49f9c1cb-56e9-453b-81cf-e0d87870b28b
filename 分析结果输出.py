#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def main():
    log_file = "test_log.log"
    output_file = "DMS加密分析结果.txt"
    
    # 打开输出文件
    with open(output_file, 'w', encoding='utf-8') as out:
        out.write("=== DMS算法加密检测工具 ===\n")
        out.write(f"分析日志文件: {log_file}\n\n")
        
        if not os.path.exists(log_file):
            out.write(f"错误：日志文件不存在 - {log_file}\n")
            return
        
        model_algorithms = []
        crypt_algorithms = []
        
        out.write("正在解析日志文件...\n")
        
        # 解析日志文件
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_number, line in enumerate(f, 1):
                line = line.strip()
                
                if "model type is" in line:
                    match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                    if match:
                        size = int(match.group(2))
                        model_algorithms.append((size, line_number, line))
                        out.write(f"  模型算法 [行{line_number}]: {size} Bytes\n")
                
                elif "[CRYPT_INFO]" in line:
                    match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                    if match:
                        size = int(match.group(1))
                        crypt_algorithms.append((size, line_number, line))
                        out.write(f"  加密算法 [行{line_number}]: {size} Bytes\n")
        
        out.write(f"\n解析完成:\n")
        out.write(f"- 找到 {len(model_algorithms)} 个模型算法\n")
        out.write(f"- 找到 {len(crypt_algorithms)} 个加密算法\n")
        
        # 排序
        model_algorithms.sort(key=lambda x: x[0])
        crypt_algorithms.sort(key=lambda x: x[0])
        
        out.write("\n=== 步骤2：排序后的算法列表 ===\n")
        out.write("模型算法 (按size从小到大):\n")
        for i, (size, line_num, _) in enumerate(model_algorithms, 1):
            out.write(f"  {i:2d}. {size:8d} Bytes [行{line_num}]\n")
        
        out.write("\n加密算法 (按size从小到大):\n")
        for i, (size, line_num, _) in enumerate(crypt_algorithms, 1):
            out.write(f"  {i:2d}. {size:8d} Bytes [行{line_num}]\n")
        
        # 匹配算法
        model_sizes = [alg[0] for alg in model_algorithms]
        crypt_sizes = [alg[0] for alg in crypt_algorithms]
        
        unmatched_models = []
        used_crypt_indices = set()
        matched_pairs = []
        
        out.write("\n=== 步骤3：算法匹配过程 ===\n")
        
        for model_size in model_sizes:
            matched = False
            
            for i, crypt_size in enumerate(crypt_sizes):
                if i in used_crypt_indices:
                    continue
                
                if model_size == crypt_size:
                    used_crypt_indices.add(i)
                    matched = True
                    matched_pairs.append((model_size, crypt_size, "普通模型"))
                    out.write(f"  ✓ 匹配: 模型{model_size} = 加密{crypt_size} (普通模型)\n")
                    break
                elif model_size == crypt_size + 32:
                    used_crypt_indices.add(i)
                    matched = True
                    matched_pairs.append((model_size, crypt_size, "NPU模型"))
                    out.write(f"  ✓ 匹配: 模型{model_size} = 加密{crypt_size}+32 (NPU模型)\n")
                    break
            
            if not matched:
                unmatched_models.append(model_size)
                out.write(f"  ✗ 未匹配: 模型{model_size} (未加密)\n")
        
        out.write(f"\n匹配结果统计:\n")
        out.write(f"  - 总模型数量: {len(model_sizes)}\n")
        out.write(f"  - 总加密数量: {len(crypt_sizes)}\n")
        out.write(f"  - 成功匹配: {len(matched_pairs)}\n")
        out.write(f"  - 未加密算法: {len(unmatched_models)}\n")
        
        # 分析未加密算法
        out.write("\n=== 步骤4：结果A分析 ===\n")
        
        if not unmatched_models:
            out.write("🎉 结果A：所有算法都已加密！\n")
        else:
            out.write("未加密算法列表:\n")
            
            kb_threshold = 10 * 1024  # 10KB
            all_under_10kb = True
            
            for i, size in enumerate(unmatched_models, 1):
                size_kb = size / 1024
                status = "✓ 符合要求" if size < kb_threshold else "✗ 超出限制"
                out.write(f"  {i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) - {status}\n")
                
                if size >= kb_threshold:
                    all_under_10kb = False
            
            out.write(f"\n{'='*60}\n")
            out.write("最终结果A:\n")
            out.write(f"  - 未加密算法数量: {len(unmatched_models)}\n")
            out.write(f"  - 10KB阈值: {kb_threshold} Bytes\n")
            
            if all_under_10kb:
                out.write("  ✅ 结论：所有未加密的算法都小于10KB - 符合安全要求\n")
            else:
                out.write("  ❌ 结论：存在大于等于10KB的未加密算法 - 不符合安全要求\n")
                large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
                out.write(f"     超出10KB限制的算法: {len(large_algorithms)} 个\n")
                for size in large_algorithms:
                    out.write(f"       - {size} Bytes ({size/1024:.2f} KB)\n")
    
    print(f"分析完成！结果已保存到 {output_file}")

if __name__ == "__main__":
    main()
