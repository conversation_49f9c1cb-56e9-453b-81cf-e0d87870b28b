import re
import os
from typing import List, <PERSON>ple
import tkinter as tk
from tkinter import filedialog, messagebox

class AlgorithmEncryptionAnalyzer:
    def __init__(self):
        self.model_info = []  # 存储 (size, line_content) 的元组
        self.crypt_info = []  # 存储 (size, line_content) 的元组

    def select_log_file(self) -> str:
        """选择日志文件"""
        root = tk.Tk()
        root.withdraw()

        file_path = filedialog.askopenfilename(
            title="选择日志文件",
            filetypes=[
                ("Log files", "*.log"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        root.destroy()
        return file_path

    def parse_log_file(self, log_path: str) -> bool:
        """解析日志文件，提取模型和加密信息"""
        if not os.path.exists(log_path):
            print(f"错误：日志文件不存在 - {log_path}")
            return False

        print(f"正在解析日志文件: {log_path}")

        self.model_info = []
        self.crypt_info = []

        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                line_number = 0
                for line in f:
                    line_number += 1
                    line = line.strip()

                    # 查找 "model type is" 开头的行
                    if line.startswith("model type is"):
                        # 匹配 size:{数字,数字} 格式，提取第二列
                        model_match = re.search(r"size:\{(\d+),\s*(\d+)\}", line)
                        if model_match:
                            size = int(model_match.group(2))  # 第二列
                            self.model_info.append((size, line, line_number))
                            print(f"找到模型信息 [行{line_number}]: size={size}")

                    # 查找 "[CRYPT_INFO]" 开头的行
                    elif line.startswith("[CRYPT_INFO]"):
                        # 匹配 size [数字] 格式
                        crypt_match = re.search(r"size\s*\[(\d+)\]", line)
                        if crypt_match:
                            size = int(crypt_match.group(1))
                            self.crypt_info.append((size, line, line_number))
                            print(f"找到加密信息 [行{line_number}]: size={size}")

        except Exception as e:
            print(f"读取日志文件时出错: {e}")
            return False

        print(f"\n解析完成:")
        print(f"- 找到 {len(self.model_info)} 个模型算法")
        print(f"- 找到 {len(self.crypt_info)} 个加密算法")

        return True

    def sort_by_size(self):
        """按size大小从小到大排序"""
        self.model_info.sort(key=lambda x: x[0])
        self.crypt_info.sort(key=lambda x: x[0])

        print("\n=== 排序后的模型算法 (按size从小到大) ===")
        for i, (size, line, line_num) in enumerate(self.model_info, 1):
            print(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")

        print("\n=== 排序后的加密算法 (按size从小到大) ===")
        for i, (size, line, line_num) in enumerate(self.crypt_info, 1):
            print(f"{i:2d}. Size: {size:8d} Bytes [行{line_num}]")

    def match_algorithms(self) -> List[int]:
        """匹配算法，返回未加密的算法大小列表"""
        model_sizes = [info[0] for info in self.model_info]
        crypt_sizes = [info[0] for info in self.crypt_info]

        unmatched_models = []
        used_crypt_indexes = set()
        matched_pairs = []

        print("\n=== 算法匹配过程 ===")

        for i, m_size in enumerate(model_sizes):
            matched = False
            for j, c_size in enumerate(crypt_sizes):
                if j in used_crypt_indexes:
                    continue

                # 检查两种匹配情况：
                # 1. 普通模型：m_size == c_size
                # 2. NPU模型：m_size == c_size + 32
                if m_size == c_size:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "普通模型"))
                    print(f"✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型)")
                    break
                elif m_size == c_size + 32:
                    used_crypt_indexes.add(j)
                    matched = True
                    matched_pairs.append((m_size, c_size, "NPU模型"))
                    print(f"✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型)")
                    break

            if not matched:
                unmatched_models.append(m_size)
                print(f"✗ 未匹配: 模型{m_size} (未加密)")

        print(f"\n=== 匹配结果统计 ===")
        print(f"总模型数量: {len(model_sizes)}")
        print(f"总加密数量: {len(crypt_sizes)}")
        print(f"成功匹配: {len(matched_pairs)}")
        print(f"未加密算法: {len(unmatched_models)}")

        return unmatched_models

    def analyze_unencrypted(self, unmatched_models: List[int]) -> bool:
        """分析未加密算法，检查是否都小于10KB"""
        if not unmatched_models:
            print("\n🎉 结果A：所有算法都已加密！")
            return True

        print(f"\n=== 未加密算法分析 ===")
        print("以下算法未加密（单位：Byte）：")

        all_under_10kb = True
        kb_threshold = 10 * 1024  # 10KB = 10240 Bytes

        for i, size in enumerate(unmatched_models, 1):
            size_kb = size / 1024
            status = "✓" if size < kb_threshold else "✗"
            print(f"{i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) {status}")

            if size >= kb_threshold:
                all_under_10kb = False

        print(f"\n{'='*50}")
        print(f"结果A分析:")
        print(f"- 未加密算法数量: {len(unmatched_models)}")
        print(f"- 10KB阈值: {kb_threshold} Bytes")

        if all_under_10kb:
            print("✅ 结果A：所有未加密的算法都小于10KB - 符合要求")
        else:
            print("❌ 结果A：存在大于等于10KB的未加密算法 - 不符合要求")
            large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
            print(f"   大于等于10KB的算法: {len(large_algorithms)} 个")
            for size in large_algorithms:
                print(f"   - {size} Bytes ({size/1024:.2f} KB)")

        return all_under_10kb

    def run_analysis(self, log_path: str = None):
        """运行完整的分析流程"""
        print("=== DMS算法加密检测工具 ===\n")

        # 选择日志文件
        if not log_path:
            log_path = self.select_log_file()
            if not log_path:
                print("未选择日志文件，程序退出")
                return

        # 解析日志文件
        if not self.parse_log_file(log_path):
            return

        # 按大小排序
        self.sort_by_size()

        # 匹配算法
        unmatched_models = self.match_algorithms()

        # 分析结果
        result = self.analyze_unencrypted(unmatched_models)

        return result

def main():
    analyzer = AlgorithmEncryptionAnalyzer()

    # 可以指定默认日志文件路径，或者留空让用户选择
    default_log_path = r"G:\24121兆岳MT8666\各种结果文件\eval\distract_eval_log\distract_positive_TL_common_daytime_015_16551_1\1.log"

    # 检查默认路径是否存在
    if os.path.exists(default_log_path):
        print(f"使用默认日志文件: {default_log_path}")
        analyzer.run_analysis(default_log_path)
    else:
        print("默认日志文件不存在，请手动选择...")
        analyzer.run_analysis()

if __name__ == "__main__":
    main()
