import re
import os

def analyze_dms_encryption():
    """
    DMS算法加密检测工具

    功能：
    1. 查看日志打印中包含"model type is"关键字的行，根据size第二列大小从小到大进行排序
    2. 查看日志打印中包含"[CRYPT_INFO]"关键字的行，根据size大小从小到大进行排序
    3. 将步骤1与步骤2中，size大小一致的项(1对1)进行消除，其中NPU的模型，"model type is"处的值比"[CRYPT_INFO]"处大32Byte
    4. 查看步骤1中未被消除的算法的大小(即未加密的算法)，得到结果A：没有加密的算法每条都小于10KB
    """

    # 日志文件路径（请按需修改）
    log_path = r"G:\24121兆岳MT8666\各种结果文件\eval\distract_eval_log\distract_positive_TL_common_night_003_16551_1\1.log"

    print("=== DMS算法加密检测工具 ===")
    print(f"尝试分析日志文件: {log_path}")

    if not os.path.exists(log_path):
        print(f"指定路径不存在，尝试使用test_log.log...")
        log_path = "test_log.log"
        if not os.path.exists(log_path):
            print(f"错误：日志文件不存在")
            print("请检查以下路径之一是否正确:")
            print("1. G:\\24121兆岳MT8666\\各种结果文件\\eval\\distract_eval_log\\distract_positive_TL_common_night_003_16551_1\\1.log")
            print("2. test_log.log (当前目录)")
            return False

    print(f"正在分析日志文件: {log_path}")


    # 存储解析结果
    model_info = []  # (size, line_number, line_content)
    crypt_info = []  # (size, line_number, line_content)

    print("\n步骤1: 解析日志文件...")

    # 解析日志文件
    try:
        with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_number = 0
            for line in f:
                line_number += 1
                line = line.strip()

                # 查找包含 "model type is" 的行
                if "model type is" in line:
                    # 匹配格式: model type is [x-y/z], size:{数字1, 数字2}
                    # 提取第二列数字
                    model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                    if model_match:
                        size = int(model_match.group(2))  # 第二列
                        model_info.append((size, line_number, line))
                        print(f"  找到模型算法 [行{line_number}]: size={size}")

                # 查找包含 "[CRYPT_INFO]" 的行
                elif "[CRYPT_INFO]" in line:
                    # 匹配格式: [CRYPT_INFO] input_data MD5 [...], size [数字], decrypt time [...]
                    crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                    if crypt_match:
                        size = int(crypt_match.group(1))
                        crypt_info.append((size, line_number, line))
                        print(f"  找到加密算法 [行{line_number}]: size={size}")

    except Exception as e:
        print(f"读取日志文件时出错: {e}")
        return False

    print(f"\n解析完成:")
    print(f"- 找到 {len(model_info)} 个模型算法")
    print(f"- 找到 {len(crypt_info)} 个加密算法")

    if not model_info or not crypt_info:
        print("未找到足够的数据进行分析")
        return False


    # 步骤2: 按size大小从小到大排序
    print("\n步骤2: 按size大小从小到大排序...")
    model_info.sort(key=lambda x: x[0])
    crypt_info.sort(key=lambda x: x[0])

    print("\n模型算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(model_info, 1):
        print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")

    print("\n加密算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(crypt_info, 1):
        print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")


    # 步骤3: 进行1对1匹配消除
    print("\n步骤3: 进行1对1匹配消除...")
    model_sizes = [info[0] for info in model_info]
    crypt_sizes = [info[0] for info in crypt_info]

    unmatched_models = []
    used_crypt_indexes = set()
    matched_pairs = []

    for m_size in model_sizes:
        matched = False
        for j, c_size in enumerate(crypt_sizes):
            if j in used_crypt_indexes:
                continue

            # 检查两种匹配情况：
            # 1. 普通模型：m_size == c_size
            # 2. NPU模型：m_size == c_size + 32
            if m_size == c_size:
                used_crypt_indexes.add(j)
                matched = True
                matched_pairs.append((m_size, c_size, "普通模型"))
                print(f"  ✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型)")
                break
            elif m_size == c_size + 32:
                used_crypt_indexes.add(j)
                matched = True
                matched_pairs.append((m_size, c_size, "NPU模型"))
                print(f"  ✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型)")
                break

        if not matched:
            unmatched_models.append(m_size)
            print(f"  ✗ 未匹配: 模型{m_size} (未加密)")

    print(f"\n匹配结果统计:")
    print(f"  - 总模型数量: {len(model_sizes)}")
    print(f"  - 总加密数量: {len(crypt_sizes)}")
    print(f"  - 成功匹配: {len(matched_pairs)}")
    print(f"  - 未加密算法: {len(unmatched_models)}")


    # 步骤4: 分析未加密算法，得到结果A
    print("\n步骤4: 分析未加密算法...")

    if not unmatched_models:
        print("🎉 结果A：所有算法都已加密！")
        return True

    print("未加密算法列表（单位：Byte）：")

    all_under_10kb = True
    kb_threshold = 10 * 1024  # 10KB = 10240 Bytes

    for i, size in enumerate(unmatched_models, 1):
        size_kb = size / 1024
        status = "✓ 符合要求" if size < kb_threshold else "✗ 超出限制"
        print(f"  {i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) - {status}")

        if size >= kb_threshold:
            all_under_10kb = False

    print(f"\n{'='*60}")
    print("结果A分析:")
    print(f"  - 未加密算法数量: {len(unmatched_models)}")
    print(f"  - 10KB阈值: {kb_threshold} Bytes")

    if all_under_10kb:
        print("  ✅ 结果A：所有未加密的算法都小于10KB - 符合安全要求")
    else:
        print("  ❌ 结果A：存在大于等于10KB的未加密算法 - 不符合安全要求")
        large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
        print(f"     超出10KB限制的算法: {len(large_algorithms)} 个")
        for size in large_algorithms:
            print(f"       - {size} Bytes ({size/1024:.2f} KB)")

    return all_under_10kb



def main():
    """主函数"""
    result = analyze_dms_encryption()
    print(f"\n分析完成，结果: {'通过安全检测' if result else '未通过安全检测'}")

if __name__ == "__main__":
    main()
