import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os
from typing import List, Tuple

class NV21ImageViewer:
    def __init__(self):
        self.images = []  # 存储加载的图像
        self.file_paths = []  # 存储文件路径
        self.current_index = 0  # 当前显示的图像索引

    def select_files(self) -> List[str]:
        """选择多个NV21文件"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        # 显示操作提示
        messagebox.showinfo(
            "文件选择提示",
            "即将打开文件选择对话框\n\n"
            "多文件选择方法：\n"
            "• 按住 Ctrl 键点击多个文件\n"
            "• 或者按住 Shift 键选择连续的文件范围\n"
            "• 或者使用 Ctrl+A 选择所有文件"
        )

        file_paths = filedialog.askopenfilenames(
            title="选择NV21图片文件（按住Ctrl键可选择多个文件）",
            filetypes=[
                ("NV21 files", "*.NV21"),
                ("All files", "*.*")
            ],
            multiple=True  # 明确指定支持多选
        )

        root.destroy()

        # 显示选择结果
        if file_paths:
            print(f"已选择 {len(file_paths)} 个文件:")
            for i, path in enumerate(file_paths, 1):
                print(f"  {i}. {os.path.basename(path)}")

        return list(file_paths)

    def select_files_alternative(self) -> List[str]:
        """备选的文件选择方法：逐个添加文件"""
        file_paths = []
        root = tk.Tk()
        root.withdraw()

        while True:
            file_path = filedialog.askopenfilename(
                title=f"选择NV21文件 (已选择{len(file_paths)}个文件)",
                filetypes=[
                    ("NV21 files", "*.NV21"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:  # 用户取消选择
                break

            if file_path not in file_paths:
                file_paths.append(file_path)
                print(f"已添加: {os.path.basename(file_path)}")
            else:
                print(f"文件已存在: {os.path.basename(file_path)}")

            # 询问是否继续添加文件
            continue_choice = messagebox.askyesno(
                "继续选择",
                f"当前已选择 {len(file_paths)} 个文件\n\n是否继续添加更多文件？"
            )

            if not continue_choice:
                break

        root.destroy()
        return file_paths

    def get_resolution(self, file_path: str) -> Tuple[int, int]:
        """获取图像分辨率"""
        # 尝试从文件名中解析分辨率
        filename = os.path.basename(file_path)

        # 常见的分辨率模式
        resolution_patterns = {
            '1920x1080': (1920, 1080),
            '1280x720': (1280, 720),
            '640x480': (640, 480),
            '320x240': (320, 240),
        }

        for pattern, resolution in resolution_patterns.items():
            if pattern in filename:
                return resolution

        # 如果无法从文件名解析，询问用户
        root = tk.Tk()
        root.withdraw()

        width = simpledialog.askinteger("分辨率设置", f"请输入 {filename} 的宽度:", initialvalue=1920)
        if width is None:
            root.destroy()
            return None

        height = simpledialog.askinteger("分辨率设置", f"请输入 {filename} 的高度:", initialvalue=1080)
        if height is None:
            root.destroy()
            return None

        root.destroy()
        return (width, height)

    def load_nv21_image(self, file_path: str, width: int, height: int) -> np.ndarray:
        """加载单个NV21图像"""
        try:
            with open(file_path, "rb") as f:
                nv21_data = f.read()

            # 检查文件大小是否匹配分辨率
            expected_size = width * height * 3 // 2
            if len(nv21_data) != expected_size:
                print(f"警告: {file_path} 文件大小({len(nv21_data)})与预期大小({expected_size})不匹配")

            # 转为 numpy 数组并重构为 YUV 图像格式
            nv21 = np.frombuffer(nv21_data, dtype=np.uint8)
            yuv_image = nv21.reshape((height * 3 // 2, width))

            # 转为 BGR 图像（OpenCV 可显示）
            bgr_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR_NV21)

            return bgr_image

        except Exception as e:
            print(f"加载图像 {file_path} 时出错: {e}")
            return None

    def load_images(self):
        """加载所有选择的图像"""
        # 让用户选择文件选择方式
        root = tk.Tk()
        root.withdraw()

        choice = messagebox.askyesnocancel(
            "文件选择方式",
            "选择文件选择方式：\n\n"
            "是(Yes): 标准多文件选择（推荐）\n"
            "否(No): 逐个添加文件\n"
            "取消: 退出程序"
        )

        root.destroy()

        if choice is None:  # 用户点击取消
            return False
        elif choice:  # 用户选择标准多文件选择
            file_paths = self.select_files()
        else:  # 用户选择逐个添加文件
            file_paths = self.select_files_alternative()

        if not file_paths:
            print("未选择任何文件")
            return False

        self.file_paths = file_paths
        self.images = []

        for file_path in file_paths:
            print(f"正在加载: {file_path}")

            resolution = self.get_resolution(file_path)
            if resolution is None:
                print(f"跳过文件: {file_path}")
                continue

            width, height = resolution
            image = self.load_nv21_image(file_path, width, height)

            if image is not None:
                self.images.append(image)
                print(f"成功加载: {os.path.basename(file_path)} ({width}x{height})")
            else:
                print(f"加载失败: {file_path}")

        return len(self.images) > 0

    def display_images_sequential(self):
        """逐个显示图像（按键切换）"""
        if not self.images:
            print("没有可显示的图像")
            return

        print(f"共加载 {len(self.images)} 张图像")
        print("按键说明:")
        print("  空格键/回车键: 下一张图片")
        print("  'p' 键: 上一张图片")
        print("  'q' 键或 ESC: 退出")
        print("  's' 键: 保存当前图片为PNG格式")

        self.current_index = 0

        while True:
            # 显示当前图像
            current_image = self.images[self.current_index]
            current_file = os.path.basename(self.file_paths[self.current_index])
            window_title = f"NV21 图像查看器 - {current_file} ({self.current_index + 1}/{len(self.images)})"

            cv2.imshow(window_title, current_image)

            # 等待按键
            key = cv2.waitKey(0) & 0xFF

            if key == ord('q') or key == 27:  # 'q' 或 ESC
                break
            elif key == ord(' ') or key == 13:  # 空格或回车
                self.current_index = (self.current_index + 1) % len(self.images)
            elif key == ord('p'):  # 'p' 上一张
                self.current_index = (self.current_index - 1) % len(self.images)
            elif key == ord('s'):  # 's' 保存
                self.save_current_image()

        cv2.destroyAllWindows()

    def save_current_image(self):
        """保存当前图像为PNG格式"""
        if not self.images:
            return

        current_image = self.images[self.current_index]
        current_file = self.file_paths[self.current_index]

        # 生成保存文件名
        base_name = os.path.splitext(os.path.basename(current_file))[0]
        save_path = f"{base_name}.png"

        # 如果文件已存在，添加序号
        counter = 1
        while os.path.exists(save_path):
            save_path = f"{base_name}_{counter}.png"
            counter += 1

        cv2.imwrite(save_path, current_image)
        print(f"图像已保存为: {save_path}")

    def display_images_grid(self, max_cols=3):
        """在网格中显示多个图像的缩略图"""
        if not self.images:
            print("没有可显示的图像")
            return

        # 计算缩略图大小
        thumbnail_size = (300, 200)

        # 创建缩略图
        thumbnails = []
        for i, image in enumerate(self.images):
            thumbnail = cv2.resize(image, thumbnail_size)

            # 添加文件名标签
            filename = os.path.basename(self.file_paths[i])
            cv2.putText(thumbnail, filename, (5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(thumbnail, f"{i+1}/{len(self.images)}", (5, thumbnail_size[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            thumbnails.append(thumbnail)

        # 计算网格布局
        num_images = len(thumbnails)
        cols = min(max_cols, num_images)
        rows = (num_images + cols - 1) // cols

        # 创建网格图像
        grid_width = cols * thumbnail_size[0]
        grid_height = rows * thumbnail_size[1]
        grid_image = np.zeros((grid_height, grid_width, 3), dtype=np.uint8)

        for i, thumbnail in enumerate(thumbnails):
            row = i // cols
            col = i % cols

            y_start = row * thumbnail_size[1]
            y_end = y_start + thumbnail_size[1]
            x_start = col * thumbnail_size[0]
            x_end = x_start + thumbnail_size[0]

            grid_image[y_start:y_end, x_start:x_end] = thumbnail

        # 显示网格
        cv2.imshow("NV21 图像网格视图", grid_image)
        print("按任意键关闭网格视图")
        cv2.waitKey(0)
        cv2.destroyAllWindows()

    def run(self):
        """运行图像查看器"""
        print("=== NV21 多图像查看器 ===")

        if not self.load_images():
            print("没有成功加载任何图像")
            return

        # 选择显示模式
        root = tk.Tk()
        root.withdraw()

        choice = messagebox.askyesnocancel(
            "显示模式选择",
            "选择显示模式:\n\n是(Yes): 逐个显示图像\n否(No): 网格显示缩略图\n取消: 退出"
        )

        root.destroy()

        if choice is True:
            self.display_images_sequential()
        elif choice is False:
            self.display_images_grid()
        else:
            print("已取消")

def main():
    viewer = NV21ImageViewer()
    viewer.run()

if __name__ == "__main__":
    main()
