import os
import re

# 文件夹路径
folder_path = r"G:\素材测试"

# 起始编号
start_index = int(input("请输入起始编号："))

# 支持的视频后缀
video_exts = ['.mp4', '.avi', '.mov', '.mkv']

# 提取文件名前缀中的数字用于排序
def extract_number(filename):
    match = re.match(r"(\d+)", filename)
    return int(match.group(1)) if match else float('inf')

# 获取并排序所有视频文件
video_files = [f for f in os.listdir(folder_path) if os.path.splitext(f)[1].lower() in video_exts]
video_files.sort(key=extract_number)

# 第一步：先全部重命名为临时名，避免覆盖冲突
temp_mapping = []  # [(old_video, tmp_video), (old_txt, tmp_txt)]

for idx, video_file in enumerate(video_files):
    name, ext = os.path.splitext(video_file)
    txt_file = name + '.txt'

    tmp_video = f"__tmp_{idx}{ext}"
    tmp_txt = f"__tmp_{idx}.txt"

    os.rename(os.path.join(folder_path, video_file), os.path.join(folder_path, tmp_video))

    if os.path.exists(os.path.join(folder_path, txt_file)):
        os.rename(os.path.join(folder_path, txt_file), os.path.join(folder_path, tmp_txt))
    else:
        tmp_txt = None

    temp_mapping.append((tmp_video, tmp_txt))

# 第二步：从临时名改为最终名
for i, (tmp_video, tmp_txt) in enumerate(temp_mapping):
    new_index = start_index + i
    ext = os.path.splitext(tmp_video)[1]

    new_video = f"{new_index}{ext}"
    new_txt = f"{new_index}.txt"

    os.rename(os.path.join(folder_path, tmp_video), os.path.join(folder_path, new_video))

    if tmp_txt:
        os.rename(os.path.join(folder_path, tmp_txt), os.path.join(folder_path, new_txt))

print("✅ 文件重命名完成！")
