import os
import re

# 文件夹路径
folder_path = r"G:\素材测试"

# 输入前缀（如 10001_LLC_Positive）
base_prefix = input("请输入起始前缀（如 10001_LLC_Positive）：").strip()

# 解析前缀编号和描述
match = re.match(r"(\d+)_(.+)", base_prefix)
if not match:
    print("前缀格式不正确，应为例如：10001_LLC_Positive")
    exit(1)

start_index = int(match.group(1))
suffix_text = match.group(2)

# 支持的视频后缀
video_exts = ['.mp4', '.avi', '.mov', '.mkv']


# 提取前缀数字部分用于排序
def extract_number(filename):
    match = re.match(r"(\d+)", filename)
    return int(match.group(1)) if match else float('inf')


# 找到所有视频文件并排序
video_files = [f for f in os.listdir(folder_path) if os.path.splitext(f)[1].lower() in video_exts]
video_files.sort(key=extract_number)

# 执行重命名（加前缀）
for i, video_file in enumerate(video_files):
    name, ext = os.path.splitext(video_file)
    txt_file = f"{name}.txt"

    current_index = start_index + i
    new_prefix = f"{current_index}_{suffix_text}_{name}"

    # 重命名视频
    new_video_name = f"{new_prefix}{ext}"
    os.rename(os.path.join(folder_path, video_file), os.path.join(folder_path, new_video_name))

    # 重命名对应txt（如果存在）
    txt_path = os.path.join(folder_path, txt_file)
    if os.path.exists(txt_path):
        new_txt_name = f"{new_prefix}.txt"
        os.rename(txt_path, os.path.join(folder_path, new_txt_name))

print("✅ 已成功加前缀重命名所有文件！")
