import re
import os
import sys

# 颜色代码
class Colors:
    GREEN = '\033[92m'  # 绿色
    RED = '\033[91m'    # 红色
    YELLOW = '\033[93m' # 黄色
    BLUE = '\033[94m'   # 蓝色
    RESET = '\033[0m'   # 重置颜色

def analyze_dms_encryption(log_path):
    """
    算法加密检测工具

    功能：
    1. 查看日志打印中包含"model type is"关键字的行，根据size第二列大小从小到大进行排序
    2. 查看日志打印中包含"[CRYPT_INFO]"关键字的行，根据size大小从小到大进行排序
    3. 将步骤1与步骤2中，size大小一致的项(1对1)进行消除，其中NPU的模型，"model type is"处的值比"[CRYPT_INFO]"处大32Byte
    4. 查看步骤1中未被消除的算法的大小(即未加密的算法)，得到结果A：没有加密的算法每条都小于10KB

    参数：
    log_path: 日志文件路径
    """

    print("=== 算法加密检测工具 ===")
    print(f"尝试分析日志文件: {log_path}")
    sys.stdout.flush()

    if not os.path.exists(log_path):
        print(f"错误：日志文件不存在 - {log_path}")
        print("请检查日志文件路径是否正确")
        return False

    # 检查文件信息
    try:
        file_size = os.path.getsize(log_path)
        print(f"文件大小: {file_size} 字节")
        if file_size == 0:
            print("错误：文件为空")
            return False
    except Exception as e:
        print(f"无法获取文件信息: {e}")
        return False

    # 存储解析结果
    model_info = []  # (size, line_number, line_content)
    crypt_info = []  # (size, line_number, line_content)

    print("\n步骤1: 解析日志文件...")

    # 解析日志文件 - 尝试不同编码格式
    encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'ascii', 'latin-1','utf-16']
    file_opened = False

    for encoding in encodings_to_try:
        try:
            print(f"  尝试使用编码: {encoding}")
            with open(log_path, 'r', encoding=encoding, errors='ignore') as f:
                line_number = 0
                for line in f:
                    line_number += 1
                    line = line.strip()

                    # 查找包含 "model type is" 的行
                    if "model type is" in line:
                        # 匹配格式: model type is [x-y/z], size:{数字1, 数字2}
                        # 提取第二列数字
                        model_match = re.search(r"model type is.*?size:\{(\d+),\s*(\d+)\}", line)
                        if model_match:
                            size = int(model_match.group(2))  # 第二列
                            model_info.append((size, line_number, line))
                            print(f"  找到模型算法 [行{line_number}]: size={size}")

                    # 查找包含 "[CRYPT_INFO]" 的行
                    elif "[CRYPT_INFO]" in line:
                        # 匹配格式: [CRYPT_INFO] input_data MD5 [...], size [数字], decrypt time [...]
                        crypt_match = re.search(r"\[CRYPT_INFO\].*?size\s*\[(\d+)\]", line)
                        if crypt_match:
                            size = int(crypt_match.group(1))
                            crypt_info.append((size, line_number, line))
                            print(f"  找到加密算法 [行{line_number}]: size={size}")

                print(f"  成功使用编码 {encoding} 读取文件，共 {line_number} 行")
                file_opened = True
                break

        except Exception as e:
            print(f"  编码 {encoding} 失败: {e}")
            continue

    if not file_opened:
        print("所有编码格式都无法读取文件")
        return False

    print(f"\n解析完成:")
    print(f"- 找到 {len(model_info)} 个模型算法")
    print(f"- 找到 {len(crypt_info)} 个加密算法")

    if not model_info and not crypt_info:
        print("\n调试信息：未找到任何匹配的行，显示前10行内容...")
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for i, line in enumerate(f, 1):
                    if i <= 10:
                        print(f"行{i}: {line.strip()}")
                    else:
                        break
        except Exception as e:
            print(f"读取调试信息失败: {e}")

        print("\n请检查日志文件是否包含以下关键字:")
        print("1. 'model type is' - 用于识别模型算法")
        print("2. '[CRYPT_INFO]' - 用于识别加密算法")
        return False

    if not model_info:
        print("警告：未找到模型算法信息")
    if not crypt_info:
        print("警告：未找到加密算法信息")


    # 步骤2: 按size大小从小到大排序
    print("\n步骤2: 按size大小从小到大排序...")
    model_info.sort(key=lambda x: x[0])
    crypt_info.sort(key=lambda x: x[0])

    print("\n模型算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(model_info, 1):
        print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")

    print("\n加密算法 (按size从小到大):")
    for i, (size, line_num, _) in enumerate(crypt_info, 1):
        print(f"  {i:2d}. Size: {size:8d} Bytes [行{line_num}]")


    # 步骤3: 进行1对1匹配消除
    print("\n步骤3: 进行1对1匹配消除...")
    model_sizes = [info[0] for info in model_info]
    crypt_sizes = [info[0] for info in crypt_info]

    unmatched_models = []
    used_crypt_indexes = set()
    matched_pairs = []

    for m_size in model_sizes:
        matched = False
        for j, c_size in enumerate(crypt_sizes):
            if j in used_crypt_indexes:
                continue

            # 检查两种匹配情况：
            # 1. 普通模型：m_size == c_size
            # 2. NPU模型：m_size == c_size + 32
            if m_size == c_size:
                used_crypt_indexes.add(j)
                matched = True
                matched_pairs.append((m_size, c_size, "普通模型"))
                print(f"  {Colors.GREEN}✓ 匹配: 模型{m_size} = 加密{c_size} (普通模型){Colors.RESET}")
                break
            elif m_size == c_size + 32:
                used_crypt_indexes.add(j)
                matched = True
                matched_pairs.append((m_size, c_size, "NPU模型"))
                print(f"  {Colors.GREEN}✓ 匹配: 模型{m_size} = 加密{c_size}+32 (NPU模型){Colors.RESET}")
                break

        if not matched:
            unmatched_models.append(m_size)
            print(f"  {Colors.RED}✗ 未匹配: 模型{m_size} (未加密){Colors.RESET}")

    print(f"\n匹配结果统计:")
    print(f"  - 总模型数量: {len(model_sizes)}")
    print(f"  - 总加密数量: {len(crypt_sizes)}")
    print(f"  - 成功匹配: {len(matched_pairs)}")
    print(f"  - 未加密算法: {len(unmatched_models)}")


    # 步骤4: 分析未加密算法，得到结果A
    print("\n步骤4: 分析未加密算法...")

    if not unmatched_models:
        print(f"{Colors.GREEN}🎉 结果A：所有算法都已加密！{Colors.RESET}")
        return True

    print("未加密算法列表（单位：Byte）：")

    all_under_10kb = True
    kb_threshold = 10 * 1024  # 10KB = 10240 Bytes

    for i, size in enumerate(unmatched_models, 1):
        size_kb = size / 1024
        status = "✓ 符合要求" if size < kb_threshold else "✗ 超出限制"
        print(f"  {i:2d}. {size:8d} Bytes ({size_kb:6.2f} KB) - {status}")

        if size >= kb_threshold:
            all_under_10kb = False

    print(f"\n{'='*60}")
    print("结果A分析:")
    print(f"  - 未加密算法数量: {len(unmatched_models)}")
    print(f"  - 10KB阈值: {kb_threshold} Bytes")

    # 修改判断逻辑：只要有未加密算法就不通过
    print(f"  {Colors.RED}❌ 结果A：存在未加密算法 - 不符合安全要求{Colors.RESET}")
    print(f"     未加密算法数量: {len(unmatched_models)} 个")

    if all_under_10kb:
        print("  注意：虽然所有未加密算法都小于10KB，但仍不符合安全要求")
    else:
        large_algorithms = [size for size in unmatched_models if size >= kb_threshold]
        print(f"     其中大于等于10KB的算法: {len(large_algorithms)} 个")
        for size in large_algorithms:
            print(f"       - {size} Bytes ({size/1024:.2f} KB)")

    return False  # 只要有未加密算法就返回False



def main():
    """主函数"""
    # 日志文件路径（请按需修改）
    log_path = r"G:\24121兆岳MT8666\各种结果文件\eval\distract_eval_log\distract_positive_ZF_common_daytime_018_23526_2\1.log"

    result = analyze_dms_encryption(log_path)

    # 显示最终结果，带颜色标志
    if result:
        print(f"\n{Colors.GREEN}✅ 分析完成，结果: 通过安全检测{Colors.RESET}")
    else:
        print(f"\n{Colors.RED}❌ 分析完成，结果: 未通过安全检测{Colors.RESET}")

if __name__ == "__main__":
    main()
