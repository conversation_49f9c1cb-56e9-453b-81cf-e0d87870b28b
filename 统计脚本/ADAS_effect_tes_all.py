from collections import defaultdict
import json
import os
import shutil
from tqdm import tqdm
import openpyxl
from openpyxl.styles import Border, Side

# alarmMask 到 material_scene 的映射关系
alarmMask_to_material_scene_adas = {
    1: "ldw",
    2: "fcw",
    4: "hmw",
    8: "pcw",
    16: "pvs",
    64: "zebra_crossing"
}

# 每种算法的属性定义
attributes = {
    # ADAS属性定义
    "ldw": {
        "part_attr": ("lane_type", "s_motion", "lane_readability", "road_types", "road_shape"),
    },
    "fcw": {
        "part_attr": ( ),
    },
    "hmw": {
        "part_attr": ( ),
    },
    "pcw": {
        "part_attr": ( ),
    },
    "pvs": {
        "part_attr": ( ),
    },
    "zebra_crossing": {
        "part_attr": ( ),
    },
}

# 属性值的自定义排序映射
material_scene_sorted = {

    'ldw': {
        'lane_type': ['dotted_white', 'solid_white', 'dotted_yellow', 'solid_yellow',
                      'double_white_dotted', 'double_solid_white', 'double_yellow_dotted',
                      'double_yellow_solid', 'white_dotted_solid', 'yellow_dotted_and_solid'
                      , 'thick_lane', 'fishbone_line', 'diversion_line'],
        's_motion': ['s_l_lane_change', 's_r_lane_change'],
        'lane_readability': ['clear', 'little_abrade', 'serious_abrade', 'words'],
        'road_types': ['highway', 'trunk_road', 'non_trunk_road', 'around_island', 'ramp_entrance_exit', 'winding_mountain_road', 'tunnel_entrance_exit'],
        'road_shape': ['straight', 'curve', 'ramp'],
        },
    'fcw': {

    },
    'hmw': {

    },
    'pcw': {

    },
    'pvs': {

    },
    'zebra_crossing': {

    },
}


# 根据结果文件内容提取视频和报警类型映射
def extract_alarm_info_before(result_file):
    extract_alarm_info_before = {}
    with open(result_file, 'r',  encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行
            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}")
                raise e  # 直接抛出异常，中断统计
            alarmMask_str = data.get("alarmMask", "0")
            try:
                alarmMask = int(alarmMask_str)
            except ValueError:
                alarmMask = -1
            filepath = data.get("filepath")
            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_pro_name + ext
            if video_name not in extract_alarm_info_before:
                extract_alarm_info_before[video_name] = set()
            extract_alarm_info_before[video_name].add(alarmMask)
    print(f"总共提取了 {len(extract_alarm_info_before)} 个视频结果。")
    return extract_alarm_info_before


# 根据标定文件提取视频的标定信息
def extract_annotation_info(annotation_dir, material_scene_to_alarmMask, extract_alarm_info_before, test_mode):
    video_annotations = {}
    video_file_paths = {}  # 保存视频实际路径

    # 处理fcw_hmw共用一批素材的情况
    def handle_fcw_hmw(video_pro_name, ext, first_annotation_data, root, material_scene):
        for i in range(1, 3):
            virtual_filename = f"{video_pro_name}_{i}{ext}"  # 分别给fcw、hmw虚拟两个标注结果
            # 根据i值，设置不同的 material_scene 和 alarmMask
            material_scene_value = 'fcw' if i == 1 else 'hmw'
            alarmMask_value = 2 if i == 1 else 4
            video_annotation = {
                "sample_np_type": first_annotation_data.get("sample_np_type"),
                "material_scene": material_scene_value,  # "fcw" or "hmw"
                "alarmMask": alarmMask_value
            }
            video_annotations[virtual_filename] = video_annotation   # 保存标注结果
            video_file_paths[virtual_filename] = os.path.join(root, video_pro_name + ext)  # 保存视频路径

    # 需要从标注文件中获取的键名，即属性值
    def handle_regular_annotation(video_name, first_annotation_data, material_scene, root, alarmMask):
        video_annotation = {
            "lane_type": first_annotation_data.get("lane_type"),
            "s_motion": first_annotation_data.get("s_motion"),
            "lane_readability": first_annotation_data.get("lane_readability"),
            "road_types": first_annotation_data.get("road_types"),
            "road_shape": first_annotation_data.get("road_shape"),
            "sample_np_type": first_annotation_data.get("sample_np_type"),
            "material_scene": material_scene,
            "alarmMask": alarmMask
        }
        video_annotations[video_name] = video_annotation
        video_file_paths[video_name] = os.path.join(root, video_name)

    # 遍历标定文件夹
    for root, dirs, files in os.walk(annotation_dir):
        for filename in files:
            #  不在统计结果中的素材和非标注文件不做处理
            if not filename.endswith('.txt') and filename not in extract_alarm_info_before:
                continue

            if filename in extract_alarm_info_before:
                video_pro_name, ext = os.path.splitext(os.path.basename(filename))
                video_alarm_txt_name = video_pro_name + '.txt'
                txt_file_path = os.path.join(root, video_alarm_txt_name)

                try:
                    with open(txt_file_path, 'r', errors='ignore') as file:
                        lines = file.readlines()
                        if not lines:
                            continue

                        first_annotation_data = json.loads(lines[0])

                        material_scene = first_annotation_data.get("material_scene")
                        alarmMask = material_scene_to_alarmMask.get(material_scene, 0)

                        # 处理 "fcw_hmw" 类型的标定信息
                        if material_scene == "fcw_hmw":
                            handle_fcw_hmw(video_pro_name, ext, first_annotation_data, root, material_scene)
                        else:
                            handle_regular_annotation(filename, first_annotation_data, material_scene, root, alarmMask)

                except FileNotFoundError:
                    print(f"标注文件不存在: {txt_file_path}")
                    raise
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON in file: {txt_file_path} - Error: {e}")
                    raise

    print(f"提取 {len(video_annotations)} 个视频标定结果。")
    return video_annotations, video_file_paths


def extract_alarm_info(result_file, video_annotations, test_mode):
    video_alarm_map = {}
    video_fcw_hmw_map = set()

    with open(result_file, 'r',  encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行
            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}, Error: {e}")
                raise
            alarmMask_str = data.get("alarmMask", "0")
            try:
                alarmMask = int(alarmMask_str)
            except ValueError:
                alarmMask = -1

            filepath = data.get("filepath")
            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))

            if test_mode == "ADAS_standardization":
                # 处理 "ADAS_standardization" 模式
                if any(values in ["fcw"] and video_pro_name in os.path.splitext(name)[0]
                       for name, attrs in video_annotations.items() for values in attrs.values()):
                    for i in range(1, 3):
                        modified_data = data.copy()
                        modified_data['filepath'] = filepath.replace(video_pro_name, f"{video_pro_name}_{i}")
                        video_name = os.path.splitext(os.path.basename(modified_data['filepath']))[0] + ext
                        video_alarm_map.setdefault(video_name, set()).add(alarmMask)
                    video_fcw_hmw_map.add(video_name)
                else:
                    video_alarm_map.setdefault(video_pro_name + ext, set()).add(alarmMask)
            else:
                # 处理其他模式
                video_name = video_pro_name + ext
                if video_name in video_annotations:
                    video_alarm_map.setdefault(video_name, set()).add(alarmMask)
    # print(video_alarm_map)
    return video_alarm_map


def update_filepath(video_name_old, filepath, video_paths):
    for video_src_name, src_path in video_paths.items():
        if video_name_old == video_src_name:
            return os.path.normpath(src_path)  # 返回新的文件路径，用于去除多余斜杠
    return filepath

def handle_alarm_mask_match(data, video_name, video_annotations, test_mode, alarmMask):

    if video_name in video_annotations:
        annotation = video_annotations[video_name]
        expected_alarmMask = annotation.get("alarmMask", 0)
        material_scene = annotation.get("material_scene", "unknown")
        sample_np_type = annotation.get("sample_np_type", "unknown")

        # 检查 alarmMask 是否匹配
        if data.get("alarmMask") == expected_alarmMask :
            filename = f"ADAS_{expected_alarmMask}_{material_scene}_{sample_np_type}.txt"
            # print(data)
            return filename, data  # 返回文件名和数据
        else:
            # 如果不匹配，返回空结果
            empty_result = {
                "filepath": data.get("filepath"),
                "alarmTime": []
            }
            filename = f"ADAS_{expected_alarmMask}_{material_scene}_{sample_np_type}.txt"
            return filename, empty_result
    return None, None

def split_and_merge_result_file(result_file, video_annotations, output_dir, video_paths, test_mode):
    # 确保目标目录存在
    os.makedirs(output_dir, exist_ok=True)

    detailed_files = defaultdict(lambda: defaultdict(list))

    with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}")
                raise e  # 直接抛出异常，中断统计

            alarmMask = int(data.get("alarmMask", 0))

            filepath = data.get("filepath")
            video_name_old, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_name_old + ext

            # 更新文件路径
            data['filepath'] = update_filepath(video_name_old, filepath, video_paths)

            for video_src_name, src_path in video_paths.items():
                    data['filepath'] = os.path.normpath(src_path)  # 修改文件路径，用于拆分结果

            # 处理 "fcw_hmw" 类型的情况
            for video_annotations_name, video_annotations_attr in video_annotations.items():
                video_before_name, ext1 = os.path.splitext(os.path.basename(video_annotations_name))
                video_before_name_all = video_before_name + ext1

                for values in video_annotations_attr.values():
                    if test_mode == "ADAS_standardization" and values == "fcw" and str(video_name_old) in video_before_name:
                        for video_src_name, src_path in video_paths.items():
                            if video_before_name_all == video_src_name:
                                data['filepath'] = os.path.normpath(src_path)  # 修改文件路径
                                # print(data['filepath'])
                        # 针对 fcw_hmw 的处理逻辑
                        for i in range(1, 3):
                            modified_data = data.copy()  # 复制结果，分别划分对应标注文件视频名称
                            modified_data['filepath'] = modified_data['filepath'].replace(f'{video_name_old}', f"{video_name_old}_{i}")
                            video_new_name, ext = os.path.splitext(os.path.basename(modified_data['filepath']))
                            video_name = video_new_name + ext

                            # 根据 alarmMask 检查并更新文件内容
                            filename, merged_data = handle_alarm_mask_match(data, video_name, video_annotations, test_mode, alarmMask)

                            if filename:
                                detailed_files[filename][video_name].append(merged_data)
                    else:
                        # 处理其他标注类型
                        data["alarmMask"] = alarmMask
                        filename, merged_data = handle_alarm_mask_match(data, video_name, video_annotations, test_mode, alarmMask)
                        if filename:
                            detailed_files[filename][video_name].append(merged_data)

    # 写入合并后的结果到拆分的文件中
    if detailed_files:
        print("{:^20} {:>30}".format("拆分结果", "路径"))
    for filename, videos_data in detailed_files.items():
        output_file = os.path.join(output_dir, filename)
        print(f"{filename:^10}   {output_file:>10}")

        with open(output_file, 'w') as file:
            for video_name, entries in videos_data.items():
                # 合并同一视频的结果
                merged_result = {"filepath": next(entry["filepath"] for entry in entries)}
                alarm_times = [entry.get("alarmTime") for entry in entries if "alarmTime" in entry]
                merged_result["alarmTime"] = sorted(set(alarm_time for sublist in alarm_times for alarm_time in
                                                        (sublist if isinstance(sublist, list) else [sublist])))

                file.write(json.dumps(merged_result, ensure_ascii=False) + "\n")

    print("结果文件拆分并合并完毕。")


# 拷贝视频到指定目录
def copy_videos(videos, src_path, error_dir):
    src_dir = os.path.dirname(src_path)
    if not os.path.exists(error_dir):
        os.makedirs(error_dir)
    for video_name in videos:
        dst_path = os.path.join(error_dir, video_name)
        if os.path.exists(src_dir):
            shutil.copy2(src_path, error_dir)
            print(f"copy {src_path} to {dst_path}")
        else:
            print(f"Source file does not exist: {src_path}")


def process_videos(result_file, annotation_dir, dst_dir, output_dir_path, test_mode, print_FN_FP, split_result,
                   copy_video):
    # 初始化映射关系和基础数据
    alarmMask_to_material_scene = alarmMask_to_material_scene_adas
    material_scene_to_alarmMask = {v: k for k, v in alarmMask_to_material_scene.items()}

    # 数据提取阶段
    video_alarm_map_before = extract_alarm_info_before(result_file)
    video_annotations, video_paths = extract_annotation_info(annotation_dir, material_scene_to_alarmMask,
                                                             video_alarm_map_before, test_mode)
    video_alarm_map = extract_alarm_info(result_file, video_annotations, test_mode)

    # 文件处理阶段
    print(f"*****未校对视频数：{len(video_alarm_map) - len(video_annotations)}")
    print("*******************************************************************************************************")

    if split_result:
        split_and_merge_result_file(result_file, video_annotations, output_dir_path, video_paths, test_mode)

    # 初始化统计数据结构
    stats = initialize_statistics(material_scene_sorted)

    # 核心统计逻辑
    process_statistics(video_alarm_map, video_annotations, stats)

    # 视频拷贝处理
    if copy_video:
        handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir)

    # 结果输出
    print_statistics(stats, print_FN_FP)

    return stats


def initialize_statistics(material_scene_sorted):
    """初始化统计数据结构"""
    return {
        'positive': defaultdict(lambda: defaultdict(int)),
        'negative': defaultdict(lambda: defaultdict(int)),
        'positive_correct': defaultdict(int),
        'negative_correct': defaultdict(int),
        'attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_positive': defaultdict(int),
        'total_negative': defaultdict(int),
        'material_scene_sorted': material_scene_sorted
    }


def process_statistics(video_alarm_map, video_annotations, stats):
    """处理核心统计逻辑"""
    for video_name, alarms in video_alarm_map.items():
        if video_name not in video_annotations:
            print(f"{video_name} 存在结果文件，缺失标注文件")
            continue

        annotation = video_annotations[video_name]
        material_scene = annotation.get("material_scene")
        sample_np_type = annotation.get("sample_np_type")
        alarmMask = annotation.get("alarmMask")

        # 基础样本统计
        update_base_stats(stats, material_scene, sample_np_type, alarms, alarmMask)

        # 属性级统计
        if material_scene in attributes:
            update_attribute_stats(stats, material_scene, sample_np_type, annotation, alarms, alarmMask)


def update_base_stats(stats, material_scene, sample_np_type, alarms, alarmMask):
    """更新基础样本统计"""
    if sample_np_type == "positive":
        stats['total_positive'][material_scene] += 1
        if alarmMask in alarms:
            stats['positive_correct'][material_scene] += 1
        else:
            stats['positive'][material_scene]['total'] += 1
    elif sample_np_type == "negative":
        stats['total_negative'][material_scene] += 1
        if alarmMask not in alarms:
            stats['negative_correct'][material_scene] += 1
        else:
            stats['negative'][material_scene]['total'] += 1


def update_attribute_stats(stats, material_scene, sample_np_type, annotation, alarms, alarmMask):
    """更新属性级统计"""
    for attr in attributes[material_scene]["part_attr"]:
        value = annotation.get(attr)
        if not value: continue

        value_str = ','.join(map(str, value)) if isinstance(value, list) else value
        key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

        # 更新总数
        stats[f'total_{key.split("_")[1]}'][value_str] += 1

        # 更新各属性值总数
        if (sample_np_type == "positive" ) or  (sample_np_type == "negative" ):
            stats[f'total_{key}'][material_scene][attr][value_str] += 1

        # 更新错误计数
        if (sample_np_type == "positive" and alarmMask not in alarms) or \
                (sample_np_type == "negative" and alarmMask in alarms):
            stats[key][material_scene][attr][value_str] += 1


def handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir):
    """处理视频拷贝逻辑（仅显示进度条）"""

    def copy_files(sample_np_type, error_type):
        # 先收集需要处理的文件
        process_files = []
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_np_type") == sample_np_type:
                if video_name in video_alarm_map:
                    alarms = video_alarm_map[video_name]
                    alarmMask = annotation.get("alarmMask")
                    condition = (alarmMask not in alarms) if sample_np_type == "positive" else (alarmMask in alarms)
                    if condition:
                        process_files.append(video_name)

        # 创建进度条
        with tqdm(total=len(process_files),
                 desc=f"{'漏检视频' if sample_np_type == 'positive' else '误检视频'}",
                 unit="file") as pbar:
            for video_name in process_files:
                if src_path := video_paths.get(video_name):
                    alarmMask = video_annotations[video_name].get("alarmMask")
                    error_dir = os.path.join(
                        dst_dir,
                        error_type,
                        f"ADAS_{alarmMask_to_material_scene_adas.get(alarmMask, 'unknown')}"
                    )
                    os.makedirs(error_dir, exist_ok=True)
                    shutil.copy2(src_path, error_dir)
                pbar.update(1)  # 无论成功失败都更新进度

    print("\n\033[32m=== 开始视频拷贝 ===\033[0m")
    copy_files("positive", "漏检视频")
    copy_files("negative", "误检视频")
    print("\033[32m=== 拷贝完成 ===\033[0m\n")


def print_statistics(stats, print_details):
    """打印总数统计"""
    print(
        "\n\033[32m***************************************************************每种算法的正样本总数和负样本总数************************************************************\033[0m")
    """打印统计结果"""
    # 打印基础统计
    print_header = ("算法", "正样本总数", "正样本漏检数", "正样本正检数", "召回率",
                    "负样本总数", "误检总数", "精准率")
    print("{:<40} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}".format(*print_header))

    for material_scene in stats['material_scene_sorted']:
        tp = stats['total_positive'][material_scene]   # 正样本总数
        fp = stats['negative'][material_scene]['total']  # 误检数
        tn = stats['total_negative'][material_scene]  # 负样本总数
        fn = stats['positive'][material_scene]['total']  # 漏检数
        correct_p = stats['positive_correct'][material_scene]  # 正样本正检数
        correct_n = stats['negative_correct'][material_scene]  # 负样本正检数

        recall = safe_division(correct_p, tp) * 100
        precision = safe_division(correct_p, (correct_p + fp)) * 100
        if (tp or tn ) > 0:
            print(f"{material_scene:<40}{tp:>8}\t{fn:>10}\t"
                  f"{correct_p:>15}\t{recall:>12.2f}%\t"
                  f"{tn:>10}\t{fp:>10}\t{precision:>12.2f}%")

    # 打印详细属性统计
    if print_details:
        print_attribute_stats(stats, "positive", "漏检")
        print_attribute_stats(stats, "negative", "误检")


def print_attribute_stats(stats, sample_np_type, error_type):
    """打印属性级统计"""
    print(
        "\n\033[32m************************************************每种属性值的{:^5}数************************************************\033[0m".format(
            error_type))

    # 确定指标类型和计算方式
    rate_name = "召回率" if sample_np_type == "positive" else "误检率"
    sample_np_type_name = "正样本" if sample_np_type == "positive" else "负样本"
    key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

    # 强制按 material_scene_sorted 的顺序遍历场景
    for material_scene in material_scene_sorted:
        # 跳过没有统计数据的场景
        if material_scene not in stats[key]:
            continue

        print(f"\033[36m{'*' * 20} {material_scene.center(20)} {'*' * 20}\033[0m")
        print("{:^25} {:<35} {:>20} {:>10} {:>10} {:>10}".format(
            "属性", "属性值", f"{sample_np_type_name}总数", f"{error_type}数", "正检数",  rate_name))

        # 按预定义的属性顺序遍历
        for attr in material_scene_sorted[material_scene]:
            # 跳过没有数据的属性
            if attr not in stats[key][material_scene]:
                continue

            print(f"{attr:^20}")
            for value in material_scene_sorted[material_scene][attr]:
                errors = stats[key][material_scene][attr].get(value, 0)
                total = stats[f'total_{key}'][material_scene][attr].get(value, 0)
                correct = total - errors

                # 动态计算比率
                if sample_np_type == "positive":
                    rate = safe_division(correct, total) * 100  # 召回率 = 正确数/正样本总数
                else:
                    rate = safe_division(errors, total) * 100  # 误检率 = 误检数/负样本总数

                print(f"{'':^35} {value:<35} "
                      f"{total:>14} "
                      f"{errors:>12} "
                      f"{correct if sample_np_type == 'positive' else total - errors:>13} "
                      f"{rate:>13.2f}%")


def safe_division(numerator, denominator):
    """安全除法"""
    return numerator / denominator if denominator else -1

def write_results_to_excel(output_dir, material_scene_sorted, stats):
    from openpyxl.styles import Font, Alignment, PatternFill

    def apply_style(sheet):
        """统一应用样式"""
        header_fill = PatternFill(start_color="3CB371", end_color="3CB371", fill_type="solid")
        for row in sheet.iter_rows():
            for cell in row:
                cell.font = Font(name="Microsoft YaHei")
                cell.alignment = Alignment(horizontal="center", vertical="center")

        # 第一行样式（表头）
        for cell in sheet[1]:
            cell.fill = header_fill
            cell.font = Font(bold=True, name="Microsoft YaHei")

        # 第二列左对齐
        for row in sheet.iter_rows(min_row=2, min_col=2, max_col=2):
            for cell in row: cell.alignment = Alignment(horizontal="left")

        # 合并“汇总”和其后一列的单元格
        sheet.merge_cells(start_row=2, start_column=1, end_row=2, end_column=2)

    def add_attribute_rows(sheet, scene, attrs):
        """填充属性数据"""
        start_row = 3
        current_attr = None

        for attr in material_scene_sorted[scene]:
            for value in material_scene_sorted[scene][attr]:
                # 获取统计数据
                pos_total = stats['total_attribute_positive'][scene].get(attr, {}).get(value, 0)  # 正样本子属性总数
                pos_error = stats['attribute_positive'][scene].get(attr, {}).get(value, 0)  # 正样本子属性漏检数量
                neg_total = stats['total_attribute_negative'][scene].get(attr, {}).get(value, 0)
                neg_error = stats['attribute_negative'][scene].get(attr, {}).get(value, 0)

                # 计算结果
                correct = pos_total - pos_error  # 正样本正检数

                # 添加行
                row = [
                    attr, value, pos_total, pos_error, correct,
                    f"{correct / pos_total * 100:.2f}%" if pos_total else "#DIV/0!",  # 召回率
                    neg_total, neg_error, neg_total - neg_error,
                    f"{neg_error / neg_total * 100:.2f}%" if neg_total else "#DIV/0!",  # 误检率
                    f"{(correct / (correct + neg_error)) * 100:.2f}%" if (correct + neg_error) else "#DIV/0!"
                ]
                sheet.append(row)

                # 合并属性列
                if attr != current_attr:
                    if current_attr:
                        sheet.merge_cells(f"A{start_row}:A{sheet.max_row - 1}")
                        # 在合并区域的第一行添加“上框线”
                        for cell in sheet[start_row]:
                            cell.border = Border(top=Side(style='thin'))
                        # 在合并区域的最后一行添加“下框线”
                        for cell in sheet[sheet.max_row - 1]:
                            cell.border = Border(bottom=Side(style='thin'))
                    current_attr = attr
                    start_row = sheet.max_row

        # 手动合并最后一个属性列
        if current_attr:
            sheet.merge_cells(f"A{start_row}:A{sheet.max_row}")
            # 在合并区域的第一行添加“上框线”
            for cell in sheet[start_row]:
                cell.border = Border(top=Side(style='thin'))
            # 在合并区域的最后一行添加“下框线”
            for cell in sheet[sheet.max_row]:
                cell.border = Border(bottom=Side(style='thin'))

    def auto_adjust_column_width(sheet):
        """自适应调整"""
        for col in sheet.columns:
            max_length = 0
            column = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    # 计算单元格内容的长度
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
                except:
                    pass
            # 设置列宽为最大宽度 + 2（留出一些额外空间）
            adjusted_width = (max_length + 8)
            sheet.column_dimensions[column].width = adjusted_width

            sheet.row_dimensions[1].height = 30  # 设置第一行的行高
            sheet.row_dimensions[2].height = 30  # 设置第二行的行高


    workbook = openpyxl.Workbook()

    for scene in material_scene_sorted:
        pos_total = stats['total_positive'].get(scene, 0)
        neg_total = stats['total_negative'].get(scene, 0)
        if not (pos_total or neg_total): continue

        sheet = workbook.create_sheet(scene)
        sheet.append(["属性", "属性值", "正样本总数", "正样本漏检数", "正样本正检数", "召回率",
                      "负样本总数", "负样本误检数", "负样本正检数", "误检率", "精确率"])

        # 添加汇总行
        pos_error = sum(stats['positive'][scene].values())  # 正检总数
        neg_error = sum(stats['negative'][scene].values())  # 误检总数


        sheet.append([
            "汇总", "", pos_total, pos_error, pos_total - pos_error,
            f"{(pos_total - pos_error) / pos_total * 100:.2f}%" if pos_total else "#DIV/0!",  # 召回率
            neg_total, neg_error, neg_total - neg_error,
            f"{neg_error / neg_total * 100:.2f}%" if neg_total else "#DIV/0!",  # 误检率
            f"{(pos_total - pos_error) / ((pos_total - pos_error) + neg_error) * 100:.2f}%" if ((pos_total - pos_error) + neg_error)  else "#DIV/0!"
        ])

        # 填充详细数据
        add_attribute_rows(sheet, scene, material_scene_sorted[scene])
        apply_style(sheet)
        sheet.freeze_panes = "A2"  # 冻结首行

       # 自适应调整列宽
        auto_adjust_column_width(sheet)

    if 'Sheet' in workbook: del workbook['Sheet']
    workbook.save(os.path.join(output_dir, "analysis_result.xlsx"))
    print(
        f"\n\033[32m-----------EXCEL统计表格已保存到{output_dir}\\analysis_result.xlsx\033[0m")

if __name__ == "__main__":

    source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\ADAS"
    # source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\22930曹操出行双摄项目\测试素材\caocao60_ADAS"
    # source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\23722消费类HI3516CV610项目\效果测试素材"
    # source_video_and_annotation_dir_path = r"E:\效果测试\素材\ADAS"
    # source_video_and_annotation_dir_path = r"D:\original\HI3516CV610\fcw_hmw_negative_new"
    # source_video_and_annotation_dir_path = r"D:\original\HI3516CV610\ADAS\pvs_positive"
    # source_video_and_annotation_dir_path = r"D:\original\HI3516CV610\ADAS"
    # source_video_and_annotation_dir_path = r"D:\original\HI3516CV610\pcw_positive_edit"
    # source_video_and_annotation_dir_path = r"D:\original\魔视\ARC\行人碰撞pcw"

    test_mode = "ADAS_standardization"  # fcw_hmw共用一批素材
    # test_mode = "ADAS"

    print_FN_FP = 0   # 打印漏误检详细分布
    split_result = 0  # 拆分结果文件
    copy_video = 0  # 拷贝漏误检视频
    write_excel = 0  # 生成统计结果表格

    result_file_path = r"E:\Project\22677 浩文NT98528\D5-7[22677-2]\测试结果\源结果\ADAS_result.txt"
    output_dir_path = r"E:\Project\#23849-1_赛格NT98528\D1-3[23849-1]\测试结果"  # 拆分结果储存路径
    destination_dir_path = r"E:\Project\#23849-1_赛格NT98528\D1-3[23849-1]"  # 漏误检视频储存路径

    stats = process_videos(result_file_path, source_video_and_annotation_dir_path, destination_dir_path, output_dir_path,
                   test_mode, print_FN_FP, split_result, copy_video)

    if write_excel:
        write_results_to_excel(output_dir_path, material_scene_sorted, stats)