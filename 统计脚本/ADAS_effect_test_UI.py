import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
from collections import defaultdict
import shutil
import configparser
from tqdm import tqdm
import openpyxl
from openpyxl.styles import Border, Side, Font, Alignment, PatternFill

class AdasEffectTestUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ADAS效果测试工具 v1.0")
        self.root.geometry("1300x950")
        
        # 配置文件路径
        self.config_file = "adas_config.ini"
        
        # 初始化变量
        self.init_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置
        self.load_config(show_message=False)
        
        # 初始化ADAS数据
        self.init_adas_data()
    
    def init_variables(self):
        """初始化界面变量"""
        self.source_dir = tk.StringVar()
        self.result_file = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.destination_dir = tk.StringVar()
        self.test_mode = tk.StringVar(value="ADAS_standardization")
        self.print_fn_fp = tk.BooleanVar(value=True)
        self.split_result = tk.BooleanVar(value=False)
        self.copy_video = tk.BooleanVar(value=False)
        self.export_excel = tk.BooleanVar(value=False)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 测试模式选择区域
        self.create_mode_section(main_frame, 0)
        
        # 路径配置区域
        self.create_path_section(main_frame, 1)
        
        # 功能配置区域
        self.create_function_section(main_frame, 2)
        
        # 高级配置区域
        self.create_advanced_section(main_frame, 3)
        
        # 控制按钮区域
        self.create_control_section(main_frame, 4)
        
        # 日志显示区域
        self.create_log_section(main_frame, 5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def create_mode_section(self, parent, row):
        """创建测试模式选择区域"""
        mode_frame = ttk.LabelFrame(parent, text="ADAS测试模式选择", padding="10")
        mode_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 模式选择
        ttk.Label(mode_frame, text="选择测试模式:", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.test_mode, 
                                 values=["ADAS_standardization", "ADAS"], 
                                 state="readonly", width=20, font=("Arial", 11))
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=10)
        mode_combo.bind('<<ComboboxSelected>>', self.on_mode_change)
        
        # 模式说明
        self.mode_info_label = ttk.Label(mode_frame, text="", font=("Arial", 10))
        self.mode_info_label.grid(row=0, column=2, sticky=tk.W, padx=20)
        
        # 更新模式说明
        self.update_mode_info()
    
    def create_path_section(self, parent, row):
        """创建路径配置区域"""
        path_frame = ttk.LabelFrame(parent, text="路径配置", padding="5")
        path_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        path_frame.columnconfigure(1, weight=1)
        
        # 源视频和标定目录
        ttk.Label(path_frame, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.source_dir, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.source_dir)).grid(row=0, column=2, padx=5)
        
        # 结果文件
        ttk.Label(path_frame, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.result_file, width=60).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_file(self.result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5)
        
        # 输出目录
        ttk.Label(path_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.output_dir, width=60).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.output_dir)).grid(row=2, column=2, padx=5)
        
        # 目标目录
        ttk.Label(path_frame, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.destination_dir, width=60).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.destination_dir)).grid(row=3, column=2, padx=5)
    
    def create_function_section(self, parent, row):
        """创建功能配置区域"""
        func_frame = ttk.LabelFrame(parent, text="功能配置", padding="5")
        func_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 功能开关
        ttk.Checkbutton(func_frame, text="打印漏误检详细分布", variable=self.print_fn_fp).grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拆分结果文件", variable=self.split_result).grid(row=0, column=1, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拷贝漏误检视频", variable=self.copy_video).grid(row=0, column=2, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="导出Excel报告", variable=self.export_excel).grid(row=0, column=3, sticky=tk.W, padx=5)
    
    def create_advanced_section(self, parent, row):
        """创建高级配置区域"""
        adv_frame = ttk.LabelFrame(parent, text="高级配置", padding="5")
        adv_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(adv_frame, text="编辑报警映射", command=self.edit_alarm_mapping).grid(row=0, column=0, padx=5)
        ttk.Button(adv_frame, text="编辑属性定义", command=self.edit_attributes).grid(row=0, column=1, padx=5)
        ttk.Button(adv_frame, text="编辑排序规则", command=self.edit_sorting_rules).grid(row=0, column=2, padx=5)
        ttk.Button(adv_frame, text="查看场景说明", command=self.show_scene_info).grid(row=0, column=3, padx=5)
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        ctrl_frame = ttk.Frame(parent)
        ctrl_frame.grid(row=row, column=0, columnspan=3, pady=10)
        
        ttk.Button(ctrl_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="编辑配置", command=self.edit_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="重置配置", command=self.reset_config).pack(side=tk.LEFT, padx=5)
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def init_adas_data(self):
        """初始化ADAS数据"""
        # ADAS报警掩码映射
        self.alarmMask_to_material_scene = {
            1: "ldw",
            2: "fcw", 
            4: "hmw",
            8: "pcw",
            16: "pvs",
            64: "zebra_crossing"
        }
        
        # ADAS属性定义
        self.attributes = {
            "ldw": {
                "part_attr": ("lane_type", "s_motion", "lane_readability", "road_types", "road_shape"),
            },
            "fcw": {
                "part_attr": (),
            },
            "hmw": {
                "part_attr": (),
            },
            "pcw": {
                "part_attr": (),
            },
            "pvs": {
                "part_attr": (),
            },
            "zebra_crossing": {
                "part_attr": (),
            },
        }
        
        # ADAS排序规则
        self.material_scene_sorted = {
            'ldw': {
                'lane_type': ['dotted_white', 'solid_white', 'dotted_yellow', 'solid_yellow',
                              'double_white_dotted', 'double_solid_white', 'double_yellow_dotted',
                              'double_yellow_solid', 'white_dotted_solid', 'yellow_dotted_and_solid',
                              'thick_lane', 'fishbone_line', 'diversion_line'],
                's_motion': ['s_l_lane_change', 's_r_lane_change'],
                'lane_readability': ['clear', 'little_abrade', 'serious_abrade', 'words'],
                'road_types': ['highway', 'trunk_road', 'non_trunk_road', 'around_island', 'ramp_entrance_exit', 'winding_mountain_road', 'tunnel_entrance_exit'],
                'road_shape': ['straight', 'curve', 'ramp'],
            },
            'fcw': {},
            'hmw': {},
            'pcw': {},
            'pvs': {},
            'zebra_crossing': {},
        }
    
    def on_mode_change(self, event=None):
        """模式改变时的处理"""
        self.update_mode_info()
        self.log_message(f"切换到 {self.test_mode.get()} 模式")
    
    def update_mode_info(self):
        """更新模式说明"""
        mode_info = {
            "ADAS_standardization": "ADAS标准化模式 - FCW/HMW共用一批素材",
            "ADAS": "ADAS普通模式 - 独立素材测试"
        }
        info_text = mode_info.get(self.test_mode.get(), "")
        self.mode_info_label.config(text=info_text)
    
    def browse_directory(self, var):
        """浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)
    
    def browse_file(self, var, title, filetypes):
        """浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def edit_alarm_mapping(self):
        """编辑报警映射"""
        self.show_config_editor("ADAS报警映射配置", self.alarmMask_to_material_scene)

    def edit_attributes(self):
        """编辑属性定义"""
        self.show_config_editor("ADAS属性定义配置", self.attributes)

    def edit_sorting_rules(self):
        """编辑排序规则"""
        self.show_config_editor("ADAS排序规则配置", self.material_scene_sorted)

    def show_scene_info(self):
        """显示场景说明"""
        info_window = tk.Toplevel(self.root)
        info_window.title("ADAS场景说明")
        info_window.geometry("800x600")

        # 创建文本显示区域
        text_area = scrolledtext.ScrolledText(info_window, wrap=tk.WORD)
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        scene_info = """
ADAS（高级驾驶辅助系统）场景说明：

支持的6种检测场景：

1. LDW (车道偏离预警) - 报警掩码: 1
   检测车辆是否偏离车道线，包括左偏、右偏等情况
   属性分析：车道线类型、车辆运动状态、车道线清晰度、道路类型、道路形状

2. FCW (前向碰撞预警) - 报警掩码: 2
   检测前方车辆或障碍物，预警可能的碰撞风险

3. HMW (车距监测预警) - 报警掩码: 4
   监测与前车的距离，当距离过近时发出预警

4. PCW (行人碰撞预警) - 报警掩码: 8
   检测前方行人，预警可能的碰撞风险

5. PVS (行人可视化系统) - 报警掩码: 16
   提供行人检测和可视化功能

6. Zebra Crossing (斑马线检测) - 报警掩码: 64
   检测斑马线区域，提供相关预警

测试模式：
• ADAS_standardization: FCW/HMW共用一批素材的标准化测试
• ADAS: 独立素材的普通测试

属性维度（以LDW为例）：
• lane_type: 车道线类型（虚线白、实线白、虚线黄等）
• s_motion: 车辆运动状态（左变道、右变道）
• lane_readability: 车道线清晰度（清晰、轻微磨损、严重磨损等）
• road_types: 道路类型（高速公路、主干道、非主干道等）
• road_shape: 道路形状（直道、弯道、坡道）
        """

        text_area.insert(tk.END, scene_info)
        text_area.config(state=tk.DISABLED)

    def show_config_editor(self, title, data):
        """显示配置编辑器"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title(title)
        editor_window.geometry("800x600")

        # 创建文本编辑器
        text_editor = scrolledtext.ScrolledText(editor_window, wrap=tk.WORD)
        text_editor.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 插入当前配置
        text_editor.insert(tk.END, json.dumps(data, indent=2, ensure_ascii=False))

        # 保存按钮
        def save_config():
            try:
                new_data = json.loads(text_editor.get(1.0, tk.END))
                # 根据标题更新对应的配置
                if "报警映射" in title:
                    self.alarmMask_to_material_scene = new_data
                elif "属性定义" in title:
                    self.attributes = new_data
                elif "排序规则" in title:
                    self.material_scene_sorted = new_data

                messagebox.showinfo("成功", "配置已保存")
                editor_window.destroy()
            except json.JSONDecodeError:
                messagebox.showerror("错误", "JSON格式错误，请检查配置")

        ttk.Button(editor_window, text="保存", command=save_config).pack(pady=5)

    def save_config(self):
        """保存配置到文件"""
        config = configparser.ConfigParser()
        config['PATHS'] = {
            'source_dir': self.source_dir.get(),
            'result_file': self.result_file.get(),
            'output_dir': self.output_dir.get(),
            'destination_dir': self.destination_dir.get()
        }
        config['SETTINGS'] = {
            'test_mode': self.test_mode.get(),
            'print_fn_fp': str(self.print_fn_fp.get()),
            'split_result': str(self.split_result.get()),
            'copy_video': str(self.copy_video.get()),
            'export_excel': str(self.export_excel.get())
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            messagebox.showinfo("成功", "配置已保存")
            self.log_message("配置已保存到文件")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self, show_message=True):
        """从文件加载配置"""
        if not os.path.exists(self.config_file):
            if show_message:
                messagebox.showinfo("提示", f"配置文件 {self.config_file} 不存在")
            return

        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')

            if 'PATHS' in config:
                self.source_dir.set(config['PATHS'].get('source_dir', ''))
                self.result_file.set(config['PATHS'].get('result_file', ''))
                self.output_dir.set(config['PATHS'].get('output_dir', ''))
                self.destination_dir.set(config['PATHS'].get('destination_dir', ''))

            if 'SETTINGS' in config:
                self.test_mode.set(config['SETTINGS'].get('test_mode', 'ADAS_standardization'))
                self.print_fn_fp.set(config['SETTINGS'].getboolean('print_fn_fp', True))
                self.split_result.set(config['SETTINGS'].getboolean('split_result', False))
                self.copy_video.set(config['SETTINGS'].getboolean('copy_video', False))
                self.export_excel.set(config['SETTINGS'].getboolean('export_excel', False))

            # 更新UI
            self.on_mode_change()

            if show_message:
                messagebox.showinfo("成功", "配置已加载")
                self.log_message("配置文件加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def edit_config(self):
        """编辑配置界面"""
        config_window = tk.Toplevel(self.root)
        config_window.title("ADAS配置编辑器")
        config_window.geometry("700x600")
        config_window.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(config_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 路径配置标签页
        path_frame = ttk.Frame(notebook, padding="10")
        notebook.add(path_frame, text="路径配置")

        # 功能配置标签页
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="功能配置")

        # 创建配置界面
        self.create_path_config_tab(path_frame)
        self.create_settings_config_tab(settings_frame)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 保存和取消按钮
        ttk.Button(button_frame, text="保存配置", command=lambda: self.save_config_from_editor(config_window)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用设置", command=self.apply_config_from_editor).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

    def create_path_config_tab(self, parent):
        """创建路径配置标签页"""
        # 源视频和标定目录
        ttk.Label(parent, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_source_dir = tk.StringVar(value=self.source_dir.get())
        source_entry = ttk.Entry(parent, textvariable=self.edit_source_dir, width=50)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_source_dir)).grid(row=0, column=2, padx=5, pady=5)

        # 结果文件
        ttk.Label(parent, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_result_file = tk.StringVar(value=self.result_file.get())
        result_entry = ttk.Entry(parent, textvariable=self.edit_result_file, width=50)
        result_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_file_for_edit(self.edit_result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5, pady=5)

        # 输出目录
        ttk.Label(parent, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_output_dir = tk.StringVar(value=self.output_dir.get())
        output_entry = ttk.Entry(parent, textvariable=self.edit_output_dir, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_output_dir)).grid(row=2, column=2, padx=5, pady=5)

        # 目标目录
        ttk.Label(parent, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_destination_dir = tk.StringVar(value=self.destination_dir.get())
        dest_entry = ttk.Entry(parent, textvariable=self.edit_destination_dir, width=50)
        dest_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_destination_dir)).grid(row=3, column=2, padx=5, pady=5)

        # 配置列权重
        parent.columnconfigure(1, weight=1)

    def create_settings_config_tab(self, parent):
        """创建功能配置标签页"""
        # 测试模式
        ttk.Label(parent, text="测试模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_test_mode = tk.StringVar(value=self.test_mode.get())
        mode_combo = ttk.Combobox(parent, textvariable=self.edit_test_mode, values=["ADAS_standardization", "ADAS"], state="readonly", width=20)
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 功能开关
        self.edit_print_fn_fp = tk.BooleanVar(value=self.print_fn_fp.get())
        ttk.Checkbutton(parent, text="打印漏误检详细分布", variable=self.edit_print_fn_fp).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_split_result = tk.BooleanVar(value=self.split_result.get())
        ttk.Checkbutton(parent, text="拆分结果文件", variable=self.edit_split_result).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_copy_video = tk.BooleanVar(value=self.copy_video.get())
        ttk.Checkbutton(parent, text="拷贝漏误检视频", variable=self.edit_copy_video).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_export_excel = tk.BooleanVar(value=self.export_excel.get())
        ttk.Checkbutton(parent, text="导出Excel报告", variable=self.edit_export_excel).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # 说明文本
        info_text = tk.Text(parent, height=10, width=60, wrap=tk.WORD)
        info_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=10)

        info_content = """ADAS效果测试工具配置说明：

• 测试模式：
  - ADAS_standardization: FCW/HMW共用一批素材的标准化测试
  - ADAS: 独立素材的普通测试

• 功能选项：
  - 打印漏误检详细分布：显示每种属性值的详细统计分析
  - 拆分结果文件：将结果按场景和样本类型拆分保存到不同文件
  - 拷贝漏误检视频：将检测错误的视频分类拷贝到指定目录
  - 导出Excel报告：生成Excel格式的详细统计报告

• 路径配置：
  - 源视频和标定目录：包含测试视频和对应标定文件的目录
  - 结果文件：算法输出的检测结果文件（JSON Lines格式）
  - 输出目录：拆分结果文件的保存位置
  - 目标目录：错误视频的分类保存位置

• ADAS场景：
  - LDW: 车道偏离预警
  - FCW: 前向碰撞预警
  - HMW: 车距监测预警
  - PCW: 行人碰撞预警
  - PVS: 行人可视化系统
  - Zebra Crossing: 斑马线检测"""

        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # 配置行列权重
        parent.columnconfigure(1, weight=1)
        parent.rowconfigure(5, weight=1)

    def browse_directory_for_edit(self, var):
        """为编辑器浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)

    def browse_file_for_edit(self, var, title, filetypes):
        """为编辑器浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)

    def apply_config_from_editor(self):
        """从编辑器应用配置到主界面"""
        self.source_dir.set(self.edit_source_dir.get())
        self.result_file.set(self.edit_result_file.get())
        self.output_dir.set(self.edit_output_dir.get())
        self.destination_dir.set(self.edit_destination_dir.get())
        self.test_mode.set(self.edit_test_mode.get())
        self.print_fn_fp.set(self.edit_print_fn_fp.get())
        self.split_result.set(self.edit_split_result.get())
        self.copy_video.set(self.edit_copy_video.get())
        self.export_excel.set(self.edit_export_excel.get())

        # 更新UI
        self.on_mode_change()

        self.log_message("配置已应用到主界面")
        messagebox.showinfo("成功", "配置已应用到主界面")

    def save_config_from_editor(self, window):
        """从编辑器保存配置"""
        # 先应用到主界面
        self.apply_config_from_editor()
        # 然后保存配置
        self.save_config()
        # 关闭编辑器窗口
        window.destroy()

    def reset_config(self):
        """重置配置"""
        self.source_dir.set('')
        self.result_file.set('')
        self.output_dir.set('')
        self.destination_dir.set('')
        self.test_mode.set('ADAS_standardization')
        self.print_fn_fp.set(True)
        self.split_result.set(False)
        self.copy_video.set(False)
        self.export_excel.set(False)
        self.log_text.delete(1.0, tk.END)

        # 更新UI
        self.on_mode_change()

        self.log_message("配置已重置")

    def validate_inputs(self):
        """验证输入参数"""
        if not self.source_dir.get():
            messagebox.showerror("错误", "请选择源视频和标定目录")
            return False
        if not self.result_file.get():
            messagebox.showerror("错误", "请选择结果文件")
            return False
        if not os.path.exists(self.source_dir.get()):
            messagebox.showerror("错误", "源目录不存在")
            return False
        if not os.path.exists(self.result_file.get()):
            messagebox.showerror("错误", "结果文件不存在")
            return False
        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        self.log_message("开始处理ADAS数据...")

        # 在新线程中运行处理逻辑
        self.progress.start()
        thread = threading.Thread(target=self.run_processing)
        thread.daemon = True
        thread.start()

    def run_processing(self):
        """运行处理逻辑"""
        try:
            # 调用ADAS处理逻辑
            self.process_adas_videos()
            self.log_message("处理完成！")

        except Exception as e:
            self.log_message(f"处理出错: {str(e)}")
        finally:
            self.progress.stop()

    def process_adas_videos(self):
        """处理ADAS视频的核心逻辑"""
        # 从原始脚本导入核心处理函数
        from ADAS_effect_tes_all import process_videos

        # 获取参数
        result_file = self.result_file.get()
        annotation_dir = self.source_dir.get()
        dst_dir = self.destination_dir.get()
        output_dir_path = self.output_dir.get()
        test_mode = self.test_mode.get()
        print_FN_FP = self.print_fn_fp.get()
        split_result = self.split_result.get()
        copy_video = self.copy_video.get()

        self.log_message(f"测试模式: {test_mode}")
        self.log_message(f"源目录: {annotation_dir}")
        self.log_message(f"结果文件: {result_file}")

        # 调用原始处理函数
        stats = process_videos(result_file, annotation_dir, dst_dir, output_dir_path,
                              test_mode, print_FN_FP, split_result, copy_video)

        # 如果需要导出Excel
        if self.export_excel.get() and output_dir_path:
            self.log_message("正在生成Excel报告...")
            from ADAS_effect_tes_all import write_results_to_excel, material_scene_sorted
            write_results_to_excel(output_dir_path, material_scene_sorted, stats)
            self.log_message("Excel报告已生成")

        self.log_message("ADAS处理完成")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = AdasEffectTestUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
