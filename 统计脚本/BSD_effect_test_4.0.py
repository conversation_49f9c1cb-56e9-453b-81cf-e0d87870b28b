from collections import defaultdict
import json
import os
import shutil

# alarmtype 到 alg_name 的映射关系

alarmtype_to_alg_name_bsd = {
    110: "Overlook_STM720P_daytime",
    120: "Overlook_STM720P_night",
    130: "Overlook_TL1080P_daytime",
    140: "Overlook_TL1080P_night",

    160: "Parallel_STM720P_daytime",
    161: "Parallel_STM720P_daytime_no_vehicle",
    162: "Parallel_STM720P_daytime_vehicle",
    170: "Parallel_STM720P_night",
    171: "Parallel_STM720P_night_no_vehicle",
    172: "Parallel_STM720P_night_vehicle",
    180: "Parallel_TL1080P_daytime",
    190: "Parallel_TL1080P_night",

    200: "Fisheye_STM720P",
    211: "Fisheye_STM720P_daytime_no_vehicle",
    212: "Fisheye_STM720P_daytime_vehicle",
    221: "Fisheye_STM720P_night_no_vehicle",
    222: "Fisheye_STM720P_night_vehicle",
}

# 每种算法的属性定义
attributes = {
    # BSD属性定义
    "Overlook_STM720P_daytime": {
        "part_attr": ("pedestrian_status", ),
    },
    "Overlook_STM720P_night": {
        "part_attr": ("pedestrian_status", ),
    },
    "Overlook_TL1080P_daytime": {
        "part_attr": ("pedestrian_status", ),
    },
    "Overlook_TL1080P_night": {
        "part_attr": ("pedestrian_status", ),
    },

    "Parallel_STM720P_daytime": {
        "part_attr": ("pedestrian_status", ),
    },
    "Parallel_STM720P_daytime_no_vehicle": {
        "part_attr": ("pedestrian_type", "height"),
    },
    "Parallel_STM720P_daytime_vehicle": {
        "part_attr": ("vehicle_status", "height"),
    },
    "Parallel_STM720P_night": {
        "part_attr": ("pedestrian_status", ),
    },
    "Parallel_STM720P_night_no_vehicle": {
        "part_attr": ("pedestrian_type", "height"),
    },
    "Parallel_STM720P_night_vehicle": {
        "part_attr": ("vehicle_status", "height"),
    },
    "Parallel_TL1080P_daytime": {
        "part_attr": ("pedestrian_status", ),
    },
    "Parallel_TL1080P_night": {
        "part_attr": ("pedestrian_status", ),
    },

    "Fisheye_STM720P": {
        "part_attr": ("pedestrian_type", "time", "height"),
    },
    "Fisheye_STM720P_daytime_no_vehicle": {
        "part_attr": ("pedestrian_type", "time", "height"),
    },
    "Fisheye_STM720P_daytime_vehicle": {
        "part_attr": ("height", ),
    },
    "Fisheye_STM720P_night_no_vehicle": {
        "part_attr": ("pedestrian_type", "height"),
    },
    "Fisheye_STM720P_night_vehicle": {
        "part_attr": ("height", ),
    },
}

# 属性值的自定义排序映射
alg_name_sorted = {
    "Overlook_STM720P_daytime": {
        # 'BSD_type': ['look_down', 'look_front'],
        # 'camera': ['STM_720P', 'TL_1080P'],
        # 'time': ['daytime', 'night'],
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Overlook_STM720P_night": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Overlook_TL1080P_daytime": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Overlook_TL1080P_night": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },

    "Parallel_STM720P_daytime": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Parallel_STM720P_daytime_no_vehicle": {
        'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material",
                            "walk_squat", "walk_cap", "tricycle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Parallel_STM720P_daytime_vehicle": {
        'vehicle_status': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
        'height': ['1.8m', '2.1m', '2.6m', '3.0m', '3.5m', ],
    },
    "Parallel_STM720P_night": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Parallel_STM720P_night_no_vehicle": {
        'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material",
                            "walk_squat", "walk_cap", "tricycle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Parallel_STM720P_night_vehicle": {
        'vehicle_status': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
        'height': ['1.8m', '2.1m', '2.6m', '3.0m', '3.5m', ],
    },
    "Parallel_TL1080P_daytime": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },
    "Parallel_TL1080P_night": {
        'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
    },

    "Fisheye_STM720P": {
        'pedestrian_type': ['electric_tricycle', 'walk_normal', 'electrocar', 'bicycle', 'walk_umbrella',
                            'walk_material', 'walk_squat', 'walk_cap', 'tricycle'],
        'time': ['daytime', 'night',],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Fisheye_STM720P_daytime_no_vehicle": {
        'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material",
                            "walk_squat", "walk_cap", "tricycle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Fisheye_STM720P_daytime_vehicle": {
        'pedestrian_type': ["miniature_vehicle","oversize_vehicle","special_vehicle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Fisheye_STM720P_night_no_vehicle": {
        'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material",
                            "walk_squat", "walk_cap", "tricycle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
    "Fisheye_STM720P_night_vehicle": {
        'pedestrian_type': ["miniature_vehicle","oversize_vehicle","special_vehicle"],
        'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m', ],
    },
}


# 根据结果文件内容提取视频和报警类型映射
def extract_alarm_info_before(result_file):
    extract_alarm_info_before = {}

    with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行

            try:
                data = json.loads(line)
            except json.JSONDecodeError:
                print(f"Invalid JSON format: {line}")
                continue

            alarmtype_str = data.get("alarmtype", "0")

            try:
                alarmtype = int(alarmtype_str)
            except ValueError:
                alarmtype = -1

            filepath = data.get("filepath")
            # print(filepath)

            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_pro_name + ext

            if video_name not in extract_alarm_info_before:
                extract_alarm_info_before[video_name] = set()
            extract_alarm_info_before[video_name].add(alarmtype)

    print(f"总共提取了 {len(extract_alarm_info_before)} 个视频结果。")
    return extract_alarm_info_before


# 根据标定文件提取视频的标定信息
def extract_annotation_info(annotation_dir, alg_name_to_alarmtype, extract_alarm_info_before, test_mode):
    video_annotations = {}
    video_file_paths = {}  # 保存视频实际路径

    for root, dirs, files in os.walk(annotation_dir):
        # print(root)
        # print(dirs)
        # print(files)
        for filename in files:
            if not filename.endswith('.txt') and filename not in extract_alarm_info_before:
                continue

            if filename in extract_alarm_info_before:
                # print(filename)
                video_name = filename
                # print(video_name)
                video_pro_name, ext = os.path.splitext(os.path.basename(filename))
                video_alarm_txt_name = video_pro_name + '.txt'

            # if filename.endswith('.txt'):
                # print(filename)
                txt_file_path = os.path.join(root, video_alarm_txt_name)

                try:
                    with open(txt_file_path, 'r') as file:
                        lines = file.readlines()
                        if not lines:
                            continue

                        # 只处理第一个字典对象
                        first_annotation_data = json.loads(lines[0])
                        # print(first_annotation_data)
                        alg_name = first_annotation_data.get("alg_name")

                        # 根据 alg_name 获取对应的 alarmtype
                        alarmtype = alg_name_to_alarmtype.get(alg_name, 0)

                        if test_mode == "BSD":
                            video_annotation = {
                                # BSD_part_attr
                                "BSD_type": first_annotation_data.get("BSD_type"),
                                "time": first_annotation_data.get("time"),
                                "camera": first_annotation_data.get("camera"),
                                "pedestrian_status": first_annotation_data.get("pedestrian_status"),
                                "pedestrian_type": first_annotation_data.get("pedestrian_type"),
                                "vehicle_status": first_annotation_data.get("vehicle_status"),
                                "height": first_annotation_data.get("height"),

                                "sample_type": first_annotation_data.get("sample_type"),
                                "alg_name": first_annotation_data.get("alg_name"),
                                "alarmtype": alarmtype
                            }
                        video_annotations[video_name] = video_annotation
                        # 保存实际视频文件路径
                        video_file_path = os.path.join(root, video_name)
                        video_file_paths[video_name] = video_file_path

                except FileNotFoundError:
                    print(f"File not found: {txt_file_path}")
                    pass
                except json.JSONDecodeError:
                    print(f"Error decoding JSON in file: {txt_file_path}")

    print(f"提取 {len(video_annotations)} 个视频标定结果。")
    # print(video_annotations)
    return video_annotations, video_file_paths

# 根据结果文件内容提取视频和报警类型映射
def extract_alarm_info(result_file, video_annotations):
    video_alarm_map = {}

    with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行

            try:
                data = json.loads(line)
            except json.JSONDecodeError:
                print(f"Invalid JSON format: {line}")
                continue

            alarmtype_str = data.get("alarmtype", "0")

            try:
                alarmtype = int(alarmtype_str)
            except ValueError:
                alarmtype = -1

            filepath = data.get("filepath")
            # print(filepath)

            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_pro_name + ext

            for video_annotations_name, video_annotations_attr in video_annotations.items():
                if video_name == video_annotations_name:
                    alg_name = video_annotations[video_name]['alg_name']
                    alarmtype_annotations = video_annotations[video_name]['alarmtype']

                    if ((alg_name == "Overlook_STM720P_daytime" and alarmtype == 1) or
                        (alg_name == "Overlook_STM720P_night" and alarmtype == 1) or
                        (alg_name == "Overlook_TL1080P_daytime" and alarmtype == 1) or
                        (alg_name == "Overlook_TL1080P_night" and alarmtype == 1) or

                        (alg_name == "Parallel_STM720P_daytime" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_daytime_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_TL1080P_daytime" and alarmtype == 1) or
                        (alg_name == "Parallel_TL1080P_night" and alarmtype == 1) or

                        (alg_name == "Fisheye_STM720P" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_daytime_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_night_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_night_vehicle" and alarmtype == 1)):
                        alarmtype = alarmtype_annotations
                    # print(alg_name)
                    # print(alarmtype)
                    # print(alarmtype)
                    if video_name not in video_alarm_map:
                        video_alarm_map[video_name] = set()
                    video_alarm_map[video_name].add(alarmtype)

    print(f"总共比对了 {len(video_alarm_map)} 个视频结果。")
    # print(video_alarm_map)
    return video_alarm_map


def split_and_merge_result_file(result_file, video_annotations, output_dir, video_paths, test_mode):
    # 确保目标目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 结果字典，用于存储合并后的结果
    detailed_files = defaultdict(lambda: defaultdict(list))

    # 读取结果文件并按视频文件名分类
    with open(result_file, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
            except json.JSONDecodeError:
                print(f"Invalid JSON format: {line}")
                continue

            alarmtype_str = data.get("alarmtype", 0)

            try:
                alarmtype = int(alarmtype_str)
            except ValueError:
                alarmtype = -1

            filepath = data.get("filepath")
            video_name_old, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_name_old + ext

            for video_src_name, src_path in video_paths.items():
                if video_name == video_src_name:
                    data['filepath'] = os.path.normpath(src_path)  # 修改文件路径

            for video_annotations_name, video_annotations_attr in video_annotations.items():
                if video_name == video_annotations_name:
                    alg_name = video_annotations[video_name]['alg_name']
                    alarmtype_annotations = video_annotations[video_name]['alarmtype']
                    if ((alg_name == "Overlook_STM720P_daytime" and alarmtype == 1) or
                        (alg_name == "Overlook_STM720P_night" and alarmtype == 1) or
                        (alg_name == "Overlook_TL1080P_daytime" and alarmtype == 1) or
                        (alg_name == "Overlook_TL1080P_night" and alarmtype == 1) or

                        (alg_name == "Parallel_STM720P_daytime" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_daytime_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_STM720P_night_vehicle" and alarmtype == 1) or
                        (alg_name == "Parallel_TL1080P_daytime" and alarmtype == 1) or
                        (alg_name == "Parallel_TL1080P_night" and alarmtype == 1) or

                        (alg_name == "Fisheye_STM720P" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_daytime_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_night_no_vehicle" and alarmtype == 1) or
                        (alg_name == "Fisheye_STM720P_night_vehicle" and alarmtype == 1)):
                        alarmtype = alarmtype_annotations

            # 更新数据中的 alarmtype
            data["alarmtype"] = alarmtype
            # print(alarmtype)

            if video_name in video_annotations:
                annotation = video_annotations[video_name]
                expected_alarmtype = annotation.get("alarmtype", 0)
                alg_name = annotation.get("alg_name", "unknown")
                sample_type = annotation.get("sample_type", "unknown")

                # 检查原结果中的 alarmtype 是否匹配或为空
                if data.get("alarmtype") == expected_alarmtype or data.get("alarmtype") == 0:
                    # detailed_filename = f"{test_mode}_{expected_alarmtype}_{alg_name}_{sample_type}.txt"
                    if test_mode == "BSD":
                        detailed_filename = f"{test_mode}_{alg_name}_{sample_type}.txt"
                    else:
                        detailed_filename = f"{test_mode}_{expected_alarmtype}_{alg_name}_{sample_type}.txt"
                    detailed_files[detailed_filename][video_name].append(data)
                    # print(detailed_files[detailed_filename][video_name])
                elif data.get("alarmtype") != expected_alarmtype:
                    # 如果不匹配，设置 alarmTime 为空
                    if test_mode == "BSD":
                        detailed_filename = f"{test_mode}_{alg_name}_{sample_type}.txt"
                    else:
                        detailed_filename = f"{test_mode}_{expected_alarmtype}_{alg_name}_{sample_type}.txt"
                    empty_result = {
                        "filepath": data.get("filepath"),
                        "alarmTime": []
                    }
                    detailed_files[detailed_filename][video_name].append(empty_result)

    # 写入合并后的结果到拆分的文件中
    if len(detailed_files) > 0:
        print("{:^20} {:>30}".format("拆分结果", "路径"))
    for filename, videos_data in detailed_files.items():
        output_file = os.path.join(output_dir, filename)
        print(f"{filename:^10}   {output_file:>10}")
        with open(output_file, 'w') as file:
            for video_name, entries in videos_data.items():
                # 合并同一视频的结果
                merged_result = {"filepath": next(entry["filepath"] for entry in entries)}
                alarm_times = [entry.get("alarmTime") for entry in entries if "alarmTime" in entry]
                # Flatten list of alarmTimes and remove duplicates
                merged_result["alarmTime"] = sorted(set(alarm_time for sublist in alarm_times for alarm_time in
                                                        (sublist if isinstance(sublist, list) else [sublist])))

                # Check if any entry has "detected" value
                # detected_values = [entry.get("detected") for entry in entries if "detected" in entry]
                # if detected_values:
                #     merged_result["detected"] = detected_values[0]

                file.write(json.dumps(merged_result, ensure_ascii=False) + "\n")
    # print("结果文件拆分并合并完毕。")


# 拷贝视频到指定目录
def copy_videos(videos, src_dir, dst_dir):
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)
    for video_name in videos:
        src_path = os.path.join(src_dir, video_name)
        dst_path = os.path.join(dst_dir, video_name)
        if os.path.exists(src_path):
            shutil.copy2(str(src_path), str(dst_path))
            print(f"cope {src_path} to {dst_path}")
        else:
            print(f"Source file does not exist: {src_path}")


# 主处理函数
def process_videos(result_file, annotation_dir, dst_dir, output_dir_path, test_mode, print_FN_FP, split_result, copy_video):
    if test_mode == "BSD":
        alarmtype_to_alg_name = alarmtype_to_alg_name_bsd
    else:
        print("无效选择！")

    # alg_name 到 alarmtype 的映射关系（反向映射）
    alg_name_to_alarmtype = {v: k for k, v in alarmtype_to_alg_name.items()}
    video_alarm_map_before = extract_alarm_info_before(result_file)
    video_annotations, video_paths = extract_annotation_info(annotation_dir, alg_name_to_alarmtype, video_alarm_map_before, test_mode)
    video_alarm_map = extract_alarm_info(result_file, video_annotations)
    print(f"*****未校对视频数：{len(video_alarm_map) - len(video_annotations)}")
    print("*******************************************************************************************************")
    # 拆分结果文件
    if split_result:
        split_and_merge_result_file(result_file, video_annotations, output_dir_path, video_paths, test_mode)
    print("*******************************************************************************************************")
    # 初始化统计字典
    positive_errors = defaultdict(lambda: defaultdict(int))
    negative_errors = defaultdict(lambda: defaultdict(int))
    positive_correct = defaultdict(int)
    negative_correct = defaultdict(int)
    attribute_positive_errors = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
    attribute_negative_errors = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

    positive_total_attribute_value = defaultdict(int)
    negative_total_attribute_value = defaultdict(int)

    positive_attribute_value = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
    negative_attribute_value = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

    # 统计每种 alg_name 的正样本总数和负样本总数
    total_positive_samples = defaultdict(int)
    total_negative_samples = defaultdict(int)

    for video_name, alarms in video_alarm_map.items():
        if video_name in video_annotations:
            # print(video_name)
            annotation = video_annotations[video_name]
            sample_type = annotation.get("sample_type")
            alg_name = annotation.get("alg_name")
            alarmtype = annotation.get("alarmtype")

            if sample_type == "positive":
                total_positive_samples[alg_name] += 1
                if alarmtype in alarms:
                    positive_correct[alg_name] += 1
                else:
                    positive_errors[alg_name][alarmtype_to_alg_name.get(alarmtype, "unknown")] += 1
            elif sample_type == "negative":
                total_negative_samples[alg_name] += 1
                if alarmtype not in alarms:
                    negative_correct[alg_name] += 1
                else:
                    negative_errors[alg_name][alarmtype_to_alg_name.get(alarmtype, "unknown")] += 1

            # 属性统计（只处理 part_attr）
            if alg_name in attributes:
                part_attrs = attributes[alg_name]["part_attr"]
                # print(part_attrs)
                for attr in part_attrs:
                    value = annotation.get(attr)
                    # print(value)
                    if value is not None:
                        if isinstance(value, list):
                            attribute_value = ','.join(map(str, value))
                            # print(attribute_value)
                        else:
                            attribute_value = value

                        if sample_type == "positive":
                            positive_total_attribute_value[attribute_value] += 1
                            positive_attribute_value[alg_name][attr][attribute_value] += 1
                            if alarmtype not in alarms:
                                # print(video_name)
                                attribute_positive_errors[alg_name][attr][attribute_value] += 1
                        elif sample_type == "negative":
                            negative_total_attribute_value[attribute_value] += 1
                            negative_attribute_value[alg_name][attr][attribute_value] += 1
                            if alarmtype in alarms:

                                attribute_negative_errors[alg_name][attr][attribute_value] += 1

    # 创建目标文件夹并拷贝视频
    if copy_video:
        positive_error_dir = os.path.join(dst_dir, '漏检视频')
        negative_error_dir = os.path.join(dst_dir, '误检视频')

        # 处理正样本漏检
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_type") == "positive" and video_name in video_alarm_map:
                alarms = video_alarm_map[video_name]
                alarmtype = annotation.get("alarmtype")
                if alarmtype not in alarms:
                    error_dir = os.path.join(positive_error_dir, "BSD_" + alarmtype_to_alg_name.get(alarmtype, "unknown"))
                    os.makedirs(error_dir, exist_ok=True)
                    # print(video_name)
                    src_path = video_paths.get(video_name)
                    if src_path:
                        copy_videos([video_name], os.path.dirname(src_path), error_dir)
        # 处理负样本误检
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_type") == "negative" and video_name in video_alarm_map:
                alarms = video_alarm_map[video_name]
                alarmtype = annotation.get("alarmtype")
                if alarmtype in alarms:
                    error_dir = os.path.join(negative_error_dir, "BSD_" + alarmtype_to_alg_name.get(alarmtype, "unknown"))
                    os.makedirs(error_dir, exist_ok=True)
                    src_path = video_paths.get(video_name)
                    if src_path:
                        copy_videos([video_name], os.path.dirname(src_path), error_dir)

    # 打印统计信息
    print(
        "***************************************************************每种算法的正样本总数和负样本总数************************************************************")
    print("{:<40} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}".format("算法", "正样本总数", "正样本漏检数",
                                                                           "正样本正检数", "召回率", "负样本总数",
                                                                           "误检总数", "精确率"))
    for alg_name in alg_name_sorted:
        # print(alg_name)
        # 只有当总的正负样本数都大于0时才填值
        total_positive = total_positive_samples.get(alg_name, 0)
        total_negative = total_negative_samples.get(alg_name, 0)
        if total_positive > 0 or total_negative > 0:
            if total_positive_samples[alg_name] != 0 and (
                    positive_correct[alg_name] + sum(negative_errors[alg_name].values())) != 0:
                print(
                    f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {sum(positive_errors[alg_name].values()):>10}\t"
                    f"{positive_correct[alg_name]:>15}\t {(positive_correct[alg_name] / total_positive_samples[alg_name]) * 100:>12.2f}%\t"
                    f"{total_negative_samples[alg_name]:>10}\t {sum(negative_errors[alg_name].values()):>10}\t"
                    f"{positive_correct[alg_name] / (positive_correct[alg_name] + sum(negative_errors[alg_name].values())) * 100:>12.2f}%\t")
            elif total_positive_samples[alg_name] == 0 and (
                    positive_correct[alg_name] + sum(negative_errors[alg_name].values())) != 0:
                print(
                    f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {sum(positive_errors[alg_name].values()):>10}\t"
                    f"{positive_correct[alg_name]:>15}\t {'     ':>12}\t"
                    f"{total_negative_samples[alg_name]:>10}\t {sum(negative_errors[alg_name].values()):>10}"
                    f"{positive_correct[alg_name] / (positive_correct[alg_name] + sum(negative_errors[alg_name].values())) * 100:>12.2f}%\t")
            elif total_positive_samples[alg_name] != 0 and (
                    positive_correct[alg_name] + sum(negative_errors[alg_name].values())) == 0:
                print(
                    f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {sum(positive_errors[alg_name].values()):>10}\t"
                    f"{positive_correct[alg_name]:>15}\t {(positive_correct[alg_name] / total_positive_samples[alg_name]) * 100:>12.2f}%\t"
                    f"{total_negative_samples[alg_name]:>10}\t {sum(negative_errors[alg_name].values()):>10}"
                    f"{' ':>12}\t")
            elif total_positive_samples[alg_name] == 0 and (
                    positive_correct[alg_name] + sum(negative_errors[alg_name].values())) == 0:
                print(
                    f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {sum(positive_errors[alg_name].values()):>10}\t"
                    f"{positive_correct[alg_name]:>15}\t {'       ':>12}\t"
                    f"{total_negative_samples[alg_name]:>10}\t {sum(negative_errors[alg_name].values()):>10}"
                    f"{' ':>12}\t")
    if print_FN_FP:
        print(
            "******************************************************************每种属性值的正样本漏检数***************************************************************")

        for alg_name in alg_name_sorted:
            if alg_name in attribute_positive_errors:
                print(
                    "*******************************************{:^20}*******************************************".format(
                        alg_name))
                attrs = attribute_positive_errors[alg_name]
                print("{:^25} {:<35} {:>10} {:>10} {:>10} {:>10}".format("属性", "属性值", "正样本总数", "正样本漏检数",
                                                                         "正样本正检数", "召回率"))
                for attr in alg_name_sorted.get(alg_name, {}):
                    sorted_values = alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = positive_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            recall_rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            # print(
                            #     f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {recall_rate:>12.2f}%\t")
                            print(count1)
                            # print(count)
                    else:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = positive_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            recall_rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            print(
                                f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {recall_rate:>12.2f}%\t")
                            # print(count)
            else:
                attrs = positive_attribute_value[alg_name]
                should_print_header = True
                for attr1 in positive_attribute_value.get(alg_name, {}):
                    for value1 in positive_attribute_value[alg_name].get(attr1, {}):
                        count2 = positive_attribute_value[alg_name][attr1][value1]
                        if should_print_header:
                            if count2 > 0:
                                print(
                                    "*******************************************{:^20}*******************************************".format(
                                        alg_name))
                                print("{:^25} {:<35} {:>10} {:>10} {:>10} {:>10}".format("属性", "属性值", "正样本总数",
                                                                                         "正样本漏检数",
                                                                                         "正样本正检数", "召回率"))
                                should_print_header = False

                for attr in alg_name_sorted.get(alg_name, {}):
                    sorted_values = alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = 0
                            count1 = positive_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            recall_rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            print(
                                f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {recall_rate:>12.2f}%\t")
                            # print(count)
        print(
            "******************************************************************每种属性值的负样本误检数***************************************************************")

        for alg_name in alg_name_sorted:
            if alg_name in attribute_negative_errors:
                print(
                    "*******************************************{:^20}*******************************************".format(
                        alg_name))
                attrs = attribute_negative_errors[alg_name]
                print("{:^25} {:<35} {:>10} {:>10} {:>10} {:>10}".format("属性", "属性值", "负样本总数", "负样本误检数",
                                                                         "负样本正检数", "精确率"))
                for attr in alg_name_sorted.get(alg_name, {}):
                    sorted_values = alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = negative_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            precise_rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            print(
                                f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {precise_rate:>12.2f}%\t")
                    else:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = negative_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            precise_rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            print(
                                f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {precise_rate:>12.2f}%\t")
            else:
                attrs = negative_attribute_value[alg_name]
                should_print_header = True
                for attr1 in negative_attribute_value.get(alg_name, {}):
                    for value1 in negative_attribute_value[alg_name].get(attr1, {}):
                        count2 = negative_attribute_value[alg_name][attr1][value1]
                        if should_print_header:
                            if count2 > 0:
                                print(
                                    "*******************************************{:^20}*******************************************".format(
                                        alg_name))
                                print("{:^25} {:<35} {:>10} {:>10} {:>10} {:>10}".format("属性", "属性值", "负样本总数",
                                                                                         "负样本误检数",
                                                                                         "负样本正检数", "精确率"))
                                should_print_header = False

                for attr in alg_name_sorted.get(alg_name, {}):
                    sorted_values = alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        print(f"{attr:^25}")
                        for value in sorted_values:
                            count = 0
                            count1 = negative_attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            precise_rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            print(
                                f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {precise_rate:>12.2f}%\t")

        print("*******************************************************************************************************")


if __name__ == "__main__":

    source_video_and_annotation_dir_path = r'\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\BSD\B_Fisheye_STM720P_支持车辆'
    # source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\BSD\B_Fisheye_STM720P_vehicle"
    # source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\BSD\B_Fisheye_STM720P_complex"

    # source_video_and_annotation_dir_path = r"E:\效果测试\素材\BSD"

    test_mode = "BSD"

    print_FN_FP = 1   # 打印漏误检详细分布
    split_result = 0  # 拆分结果文件
    copy_video = 0   # 拷贝漏误检视频

    result_file_path = r"G:\24121兆岳MT8666\D1-2[24121-2]\测试结果\BSD_RESULT_2.1.026.1403.116_20230324_230424\BSD_result_2.1.026.1403.116_20230324_231403.txt"
    output_dir_path = r"G:\24121兆岳MT8666\D1-2[24121-2]\测试结果"  # 拆分结果储存路径
    destination_dir_path = r"G:\24121兆岳MT8666\D1-2[24121-2]"  # 漏误检视频储存路径

    process_videos(result_file_path, source_video_and_annotation_dir_path, destination_dir_path, output_dir_path,
                   test_mode, print_FN_FP, split_result, copy_video)
