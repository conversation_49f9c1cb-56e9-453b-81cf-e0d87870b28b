import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
from collections import defaultdict
import shutil
import configparser

class BSDEffectTestUI:
    def __init__(self, root):
        self.root = root
        self.root.title("BSD效果测试工具 v4.0")
        self.root.geometry("1000x800")
        
        # 配置文件路径
        self.config_file = "bsd_config.ini"
        
        # 初始化变量
        self.init_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置（启动时不显示消息）
        self.load_config(show_message=False)
        
        # 初始化算法映射和属性
        self.init_algorithm_data()
    
    def init_variables(self):
        """初始化界面变量"""
        self.source_dir = tk.StringVar()
        self.result_file = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.destination_dir = tk.StringVar()
        self.test_mode = tk.StringVar(value="BSD")
        self.print_fn_fp = tk.BooleanVar(value=True)
        self.split_result = tk.BooleanVar(value=False)
        self.copy_video = tk.BooleanVar(value=False)
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 路径配置区域
        self.create_path_section(main_frame, 0)
        
        # 功能配置区域
        self.create_function_section(main_frame, 1)
        
        # 高级配置区域
        self.create_advanced_section(main_frame, 2)
        
        # 控制按钮区域
        self.create_control_section(main_frame, 3)
        
        # 日志显示区域
        self.create_log_section(main_frame, 4)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def create_path_section(self, parent, row):
        """创建路径配置区域"""
        path_frame = ttk.LabelFrame(parent, text="路径配置", padding="5")
        path_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        path_frame.columnconfigure(1, weight=1)
        
        # 源视频和标定目录
        ttk.Label(path_frame, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.source_dir, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.source_dir)).grid(row=0, column=2, padx=5)
        
        # 结果文件
        ttk.Label(path_frame, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.result_file, width=60).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_file(self.result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5)
        
        # 输出目录
        ttk.Label(path_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.output_dir, width=60).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.output_dir)).grid(row=2, column=2, padx=5)
        
        # 目标目录
        ttk.Label(path_frame, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.destination_dir, width=60).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.destination_dir)).grid(row=3, column=2, padx=5)
    
    def create_function_section(self, parent, row):
        """创建功能配置区域"""
        func_frame = ttk.LabelFrame(parent, text="功能配置", padding="5")
        func_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 测试模式
        ttk.Label(func_frame, text="测试模式:").grid(row=0, column=0, sticky=tk.W, padx=5)
        mode_combo = ttk.Combobox(func_frame, textvariable=self.test_mode, values=["BSD"], state="readonly", width=10)
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 功能开关
        ttk.Checkbutton(func_frame, text="打印漏误检详细分布", variable=self.print_fn_fp).grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拆分结果文件", variable=self.split_result).grid(row=1, column=1, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拷贝漏误检视频", variable=self.copy_video).grid(row=1, column=2, sticky=tk.W, padx=5)
    
    def create_advanced_section(self, parent, row):
        """创建高级配置区域"""
        adv_frame = ttk.LabelFrame(parent, text="高级配置", padding="5")
        adv_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(adv_frame, text="编辑算法映射", command=self.edit_algorithm_mapping).grid(row=0, column=0, padx=5)
        ttk.Button(adv_frame, text="编辑属性定义", command=self.edit_attributes).grid(row=0, column=1, padx=5)
        ttk.Button(adv_frame, text="编辑排序规则", command=self.edit_sorting_rules).grid(row=0, column=2, padx=5)
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        ctrl_frame = ttk.Frame(parent)
        ctrl_frame.grid(row=row, column=0, columnspan=3, pady=10)
        
        ttk.Button(ctrl_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="编辑配置", command=self.edit_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="重置配置", command=self.reset_config).pack(side=tk.LEFT, padx=5)
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def browse_directory(self, var):
        """浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)
    
    def browse_file(self, var, title, filetypes):
        """浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def init_algorithm_data(self):
        """初始化算法数据"""
        # 这里包含原脚本中的算法映射和属性定义
        self.alarmtype_to_alg_name_bsd = {
            110: "Overlook_STM720P_daytime",
            120: "Overlook_STM720P_night",
            130: "Overlook_TL1080P_daytime",
            140: "Overlook_TL1080P_night",
            160: "Parallel_STM720P_daytime",
            161: "Parallel_STM720P_daytime_no_vehicle",
            162: "Parallel_STM720P_daytime_vehicle",
            170: "Parallel_STM720P_night",
            171: "Parallel_STM720P_night_no_vehicle",
            172: "Parallel_STM720P_night_vehicle",
            180: "Parallel_TL1080P_daytime",
            190: "Parallel_TL1080P_night",
            200: "Fisheye_STM720P",
            211: "Fisheye_STM720P_daytime_no_vehicle",
            212: "Fisheye_STM720P_daytime_vehicle",
            221: "Fisheye_STM720P_night_no_vehicle",
            222: "Fisheye_STM720P_night_vehicle",
        }
        
        # 完整的属性定义
        self.attributes = {
            "Overlook_STM720P_daytime": {"part_attr": ("pedestrian_status",)},
            "Overlook_STM720P_night": {"part_attr": ("pedestrian_status",)},
            "Overlook_TL1080P_daytime": {"part_attr": ("pedestrian_status",)},
            "Overlook_TL1080P_night": {"part_attr": ("pedestrian_status",)},
            "Parallel_STM720P_daytime": {"part_attr": ("pedestrian_status",)},
            "Parallel_STM720P_daytime_no_vehicle": {"part_attr": ("pedestrian_type", "height")},
            "Parallel_STM720P_daytime_vehicle": {"part_attr": ("vehicle_status", "height")},
            "Parallel_STM720P_night": {"part_attr": ("pedestrian_status",)},
            "Parallel_STM720P_night_no_vehicle": {"part_attr": ("pedestrian_type", "height")},
            "Parallel_STM720P_night_vehicle": {"part_attr": ("vehicle_status", "height")},
            "Parallel_TL1080P_daytime": {"part_attr": ("pedestrian_status",)},
            "Parallel_TL1080P_night": {"part_attr": ("pedestrian_status",)},
            "Fisheye_STM720P": {"part_attr": ("pedestrian_type", "time", "height")},
            "Fisheye_STM720P_daytime_no_vehicle": {"part_attr": ("pedestrian_type", "time", "height")},
            "Fisheye_STM720P_daytime_vehicle": {"part_attr": ("height",)},
            "Fisheye_STM720P_night_no_vehicle": {"part_attr": ("pedestrian_type", "height")},
            "Fisheye_STM720P_night_vehicle": {"part_attr": ("height",)},
        }

        # 完整的排序规则
        self.alg_name_sorted = {
            "Overlook_STM720P_daytime": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Overlook_STM720P_night": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Overlook_TL1080P_daytime": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Overlook_TL1080P_night": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Parallel_STM720P_daytime": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Parallel_STM720P_daytime_no_vehicle": {
                'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material", "walk_squat", "walk_cap", "tricycle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Parallel_STM720P_daytime_vehicle": {
                'vehicle_status': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
                'height': ['1.8m', '2.1m', '2.6m', '3.0m', '3.5m'],
            },
            "Parallel_STM720P_night": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Parallel_STM720P_night_no_vehicle": {
                'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material", "walk_squat", "walk_cap", "tricycle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Parallel_STM720P_night_vehicle": {
                'vehicle_status': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
                'height': ['1.8m', '2.1m', '2.6m', '3.0m', '3.5m'],
            },
            "Parallel_TL1080P_daytime": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Parallel_TL1080P_night": {
                'pedestrian_status': ['walk', 'run', 'squat', 'umbrella', 'ride'],
            },
            "Fisheye_STM720P": {
                'pedestrian_type': ['electric_tricycle', 'walk_normal', 'electrocar', 'bicycle', 'walk_umbrella', 'walk_material', 'walk_squat', 'walk_cap', 'tricycle'],
                'time': ['daytime', 'night'],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Fisheye_STM720P_daytime_no_vehicle": {
                'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material", "walk_squat", "walk_cap", "tricycle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Fisheye_STM720P_daytime_vehicle": {
                'pedestrian_type': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Fisheye_STM720P_night_no_vehicle": {
                'pedestrian_type': ["walk_normal", "walk_umbrella", "electric_tricycle", "electrocar", "bicycle", "walk_material", "walk_squat", "walk_cap", "tricycle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
            "Fisheye_STM720P_night_vehicle": {
                'pedestrian_type': ["miniature_vehicle", "oversize_vehicle", "special_vehicle"],
                'height': ['1.7m', '2.0m', '2.3m', '2.6m', '2.9m'],
            },
        }

    def edit_algorithm_mapping(self):
        """编辑算法映射"""
        self.show_config_editor("算法映射配置", self.alarmtype_to_alg_name_bsd)

    def edit_attributes(self):
        """编辑属性定义"""
        self.show_config_editor("属性定义配置", self.attributes)

    def edit_sorting_rules(self):
        """编辑排序规则"""
        self.show_config_editor("排序规则配置", self.alg_name_sorted)

    def show_config_editor(self, title, data):
        """显示配置编辑器"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title(title)
        editor_window.geometry("600x400")

        # 创建文本编辑器
        text_editor = scrolledtext.ScrolledText(editor_window, wrap=tk.WORD)
        text_editor.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 插入当前配置
        text_editor.insert(tk.END, json.dumps(data, indent=2, ensure_ascii=False))

        # 保存按钮
        def save_config():
            try:
                new_data = json.loads(text_editor.get(1.0, tk.END))
                if title == "算法映射配置":
                    self.alarmtype_to_alg_name_bsd = new_data
                elif title == "属性定义配置":
                    self.attributes = new_data
                elif title == "排序规则配置":
                    self.alg_name_sorted = new_data
                messagebox.showinfo("成功", "配置已保存")
                editor_window.destroy()
            except json.JSONDecodeError:
                messagebox.showerror("错误", "JSON格式错误，请检查配置")

        ttk.Button(editor_window, text="保存", command=save_config).pack(pady=5)

    def save_config(self):
        """保存配置到文件"""
        config = configparser.ConfigParser()
        config['PATHS'] = {
            'source_dir': self.source_dir.get(),
            'result_file': self.result_file.get(),
            'output_dir': self.output_dir.get(),
            'destination_dir': self.destination_dir.get()
        }
        config['SETTINGS'] = {
            'test_mode': self.test_mode.get(),
            'print_fn_fp': str(self.print_fn_fp.get()),
            'split_result': str(self.split_result.get()),
            'copy_video': str(self.copy_video.get())
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self, show_message=True):
        """从文件加载配置"""
        if not os.path.exists(self.config_file):
            if show_message:
                messagebox.showinfo("提示", f"配置文件 {self.config_file} 不存在")
            return

        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')

            if 'PATHS' in config:
                self.source_dir.set(config['PATHS'].get('source_dir', ''))
                self.result_file.set(config['PATHS'].get('result_file', ''))
                self.output_dir.set(config['PATHS'].get('output_dir', ''))
                self.destination_dir.set(config['PATHS'].get('destination_dir', ''))

            if 'SETTINGS' in config:
                self.test_mode.set(config['SETTINGS'].get('test_mode', 'BSD'))
                self.print_fn_fp.set(config['SETTINGS'].getboolean('print_fn_fp', True))
                self.split_result.set(config['SETTINGS'].getboolean('split_result', False))
                self.copy_video.set(config['SETTINGS'].getboolean('copy_video', False))

            if show_message:
                messagebox.showinfo("成功", "配置已加载")
                self.log_message("配置文件加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def edit_config(self):
        """编辑配置界面"""
        config_window = tk.Toplevel(self.root)
        config_window.title("配置编辑器")
        config_window.geometry("600x500")
        config_window.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(config_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 路径配置标签页
        path_frame = ttk.Frame(notebook, padding="10")
        notebook.add(path_frame, text="路径配置")

        # 功能配置标签页
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="功能配置")

        # 创建路径配置界面
        self.create_path_config_tab(path_frame)

        # 创建功能配置界面
        self.create_settings_config_tab(settings_frame)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 保存和取消按钮
        ttk.Button(button_frame, text="保存配置", command=lambda: self.save_config_from_editor(config_window)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用设置", command=self.apply_config_from_editor).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

    def create_path_config_tab(self, parent):
        """创建路径配置标签页"""
        # 源视频和标定目录
        ttk.Label(parent, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_source_dir = tk.StringVar(value=self.source_dir.get())
        source_entry = ttk.Entry(parent, textvariable=self.edit_source_dir, width=50)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_source_dir)).grid(row=0, column=2, padx=5, pady=5)

        # 结果文件
        ttk.Label(parent, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_result_file = tk.StringVar(value=self.result_file.get())
        result_entry = ttk.Entry(parent, textvariable=self.edit_result_file, width=50)
        result_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_file_for_edit(self.edit_result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5, pady=5)

        # 输出目录
        ttk.Label(parent, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_output_dir = tk.StringVar(value=self.output_dir.get())
        output_entry = ttk.Entry(parent, textvariable=self.edit_output_dir, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_output_dir)).grid(row=2, column=2, padx=5, pady=5)

        # 目标目录
        ttk.Label(parent, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_destination_dir = tk.StringVar(value=self.destination_dir.get())
        dest_entry = ttk.Entry(parent, textvariable=self.edit_destination_dir, width=50)
        dest_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_destination_dir)).grid(row=3, column=2, padx=5, pady=5)

        # 配置列权重
        parent.columnconfigure(1, weight=1)

    def create_settings_config_tab(self, parent):
        """创建功能配置标签页"""
        # 测试模式
        ttk.Label(parent, text="测试模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_test_mode = tk.StringVar(value=self.test_mode.get())
        mode_combo = ttk.Combobox(parent, textvariable=self.edit_test_mode, values=["BSD"], state="readonly", width=15)
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 功能开关
        self.edit_print_fn_fp = tk.BooleanVar(value=self.print_fn_fp.get())
        ttk.Checkbutton(parent, text="打印漏误检详细分布", variable=self.edit_print_fn_fp).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_split_result = tk.BooleanVar(value=self.split_result.get())
        ttk.Checkbutton(parent, text="拆分结果文件", variable=self.edit_split_result).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_copy_video = tk.BooleanVar(value=self.copy_video.get())
        ttk.Checkbutton(parent, text="拷贝漏误检视频", variable=self.edit_copy_video).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # 说明文本
        info_text = tk.Text(parent, height=8, width=60, wrap=tk.WORD)
        info_text.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=10)

        info_content = """配置说明：

• 打印漏误检详细分布：启用后会显示每种属性值的详细统计分析
• 拆分结果文件：将结果按算法和样本类型拆分保存到不同文件
• 拷贝漏误检视频：将检测错误的视频分类拷贝到指定目录

路径配置：
• 源视频和标定目录：包含测试视频和对应标定文件的目录
• 结果文件：算法输出的检测结果文件（JSON Lines格式）
• 输出目录：拆分结果文件的保存位置
• 目标目录：错误视频的分类保存位置"""

        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # 配置行列权重
        parent.columnconfigure(1, weight=1)
        parent.rowconfigure(4, weight=1)

    def browse_directory_for_edit(self, var):
        """为编辑器浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)

    def browse_file_for_edit(self, var, title, filetypes):
        """为编辑器浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)

    def apply_config_from_editor(self):
        """从编辑器应用配置到主界面"""
        self.source_dir.set(self.edit_source_dir.get())
        self.result_file.set(self.edit_result_file.get())
        self.output_dir.set(self.edit_output_dir.get())
        self.destination_dir.set(self.edit_destination_dir.get())
        self.test_mode.set(self.edit_test_mode.get())
        self.print_fn_fp.set(self.edit_print_fn_fp.get())
        self.split_result.set(self.edit_split_result.get())
        self.copy_video.set(self.edit_copy_video.get())

        self.log_message("配置已应用到主界面")
        messagebox.showinfo("成功", "配置已应用到主界面")

    def save_config_from_editor(self, window):
        """从编辑器保存配置"""
        # 先应用到主界面
        self.apply_config_from_editor()
        # 然后保存配置
        self.save_config()
        # 关闭编辑器窗口
        window.destroy()

    def reset_config(self):
        """重置配置"""
        self.source_dir.set('')
        self.result_file.set('')
        self.output_dir.set('')
        self.destination_dir.set('')
        self.test_mode.set('BSD')
        self.print_fn_fp.set(True)
        self.split_result.set(False)
        self.copy_video.set(False)
        self.log_text.delete(1.0, tk.END)

    def validate_inputs(self):
        """验证输入参数"""
        if not self.source_dir.get():
            messagebox.showerror("错误", "请选择源视频和标定目录")
            return False
        if not self.result_file.get():
            messagebox.showerror("错误", "请选择结果文件")
            return False
        if not os.path.exists(self.source_dir.get()):
            messagebox.showerror("错误", "源目录不存在")
            return False
        if not os.path.exists(self.result_file.get()):
            messagebox.showerror("错误", "结果文件不存在")
            return False
        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        # 在新线程中运行处理逻辑
        self.progress.start()
        thread = threading.Thread(target=self.run_processing)
        thread.daemon = True
        thread.start()

    def run_processing(self):
        """运行处理逻辑"""
        try:
            self.log_message("开始处理...")

            # 调用原始处理函数
            self.process_videos(
                self.result_file.get(),
                self.source_dir.get(),
                self.destination_dir.get(),
                self.output_dir.get(),
                self.test_mode.get(),
                self.print_fn_fp.get(),
                self.split_result.get(),
                self.copy_video.get()
            )

            self.log_message("处理完成！")

        except Exception as e:
            self.log_message(f"处理出错: {str(e)}")
        finally:
            self.progress.stop()

    # 以下是原始脚本的核心处理函数，集成到UI类中
    def extract_alarm_info_before(self, result_file):
        """根据结果文件内容提取视频和报警类型映射"""
        extract_alarm_info_before = {}

        with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError:
                    self.log_message(f"Invalid JSON format: {line}")
                    continue

                alarmtype_str = data.get("alarmtype", "0")

                try:
                    alarmtype = int(alarmtype_str)
                except ValueError:
                    alarmtype = -1

                filepath = data.get("filepath")
                video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_pro_name + ext

                if video_name not in extract_alarm_info_before:
                    extract_alarm_info_before[video_name] = set()
                extract_alarm_info_before[video_name].add(alarmtype)

        self.log_message(f"总共提取了 {len(extract_alarm_info_before)} 个视频结果。")
        return extract_alarm_info_before

    def extract_annotation_info(self, annotation_dir, alg_name_to_alarmtype, extract_alarm_info_before, test_mode):
        """根据标定文件提取视频的标定信息"""
        video_annotations = {}
        video_file_paths = {}

        for root, dirs, files in os.walk(annotation_dir):
            for filename in files:
                if not filename.endswith('.txt') and filename not in extract_alarm_info_before:
                    continue

                if filename in extract_alarm_info_before:
                    video_name = filename
                    video_pro_name, ext = os.path.splitext(os.path.basename(filename))
                    video_alarm_txt_name = video_pro_name + '.txt'

                    txt_file_path = os.path.join(root, video_alarm_txt_name)

                    try:
                        with open(txt_file_path, 'r') as file:
                            lines = file.readlines()
                            if not lines:
                                continue

                            first_annotation_data = json.loads(lines[0])
                            alg_name = first_annotation_data.get("alg_name")
                            alarmtype = alg_name_to_alarmtype.get(alg_name, 0)

                            if test_mode == "BSD":
                                video_annotation = {
                                    "BSD_type": first_annotation_data.get("BSD_type"),
                                    "time": first_annotation_data.get("time"),
                                    "camera": first_annotation_data.get("camera"),
                                    "pedestrian_status": first_annotation_data.get("pedestrian_status"),
                                    "pedestrian_type": first_annotation_data.get("pedestrian_type"),
                                    "vehicle_status": first_annotation_data.get("vehicle_status"),
                                    "height": first_annotation_data.get("height"),
                                    "sample_type": first_annotation_data.get("sample_type"),
                                    "alg_name": first_annotation_data.get("alg_name"),
                                    "alarmtype": alarmtype
                                }
                            video_annotations[video_name] = video_annotation
                            video_file_path = os.path.join(root, video_name)
                            video_file_paths[video_name] = video_file_path

                    except FileNotFoundError:
                        self.log_message(f"File not found: {txt_file_path}")
                        pass
                    except json.JSONDecodeError:
                        self.log_message(f"Error decoding JSON in file: {txt_file_path}")

        self.log_message(f"提取 {len(video_annotations)} 个视频标定结果。")
        return video_annotations, video_file_paths

    def extract_alarm_info(self, result_file, video_annotations):
        """根据结果文件内容提取视频和报警类型映射"""
        video_alarm_map = {}

        with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError:
                    self.log_message(f"Invalid JSON format: {line}")
                    continue

                alarmtype_str = data.get("alarmtype", "0")

                try:
                    alarmtype = int(alarmtype_str)
                except ValueError:
                    alarmtype = -1

                filepath = data.get("filepath")
                video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_pro_name + ext

                for video_annotations_name, video_annotations_attr in video_annotations.items():
                    if video_name == video_annotations_name:
                        alg_name = video_annotations[video_name]['alg_name']
                        alarmtype_annotations = video_annotations[video_name]['alarmtype']

                        # 检查算法名称和报警类型的匹配（完全按照原脚本逻辑）
                        if ((alg_name == "Overlook_STM720P_daytime" and alarmtype == 1) or
                            (alg_name == "Overlook_STM720P_night" and alarmtype == 1) or
                            (alg_name == "Overlook_TL1080P_daytime" and alarmtype == 1) or
                            (alg_name == "Overlook_TL1080P_night" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_TL1080P_daytime" and alarmtype == 1) or
                            (alg_name == "Parallel_TL1080P_night" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_daytime_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_night_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_night_vehicle" and alarmtype == 1)):
                            alarmtype = alarmtype_annotations

                        if video_name not in video_alarm_map:
                            video_alarm_map[video_name] = set()
                        video_alarm_map[video_name].add(alarmtype)

        self.log_message(f"总共比对了 {len(video_alarm_map)} 个视频结果。")
        return video_alarm_map

    def check_algorithm_match(self, alg_name, alarmtype):
        """检查算法名称和报警类型是否匹配"""
        algorithm_matches = [
            "Overlook_STM720P_daytime", "Overlook_STM720P_night",
            "Overlook_TL1080P_daytime", "Overlook_TL1080P_night",
            "Parallel_STM720P_daytime", "Parallel_STM720P_daytime_no_vehicle",
            "Parallel_STM720P_daytime_vehicle", "Parallel_STM720P_night",
            "Parallel_STM720P_night_no_vehicle", "Parallel_STM720P_night_vehicle",
            "Parallel_TL1080P_daytime", "Parallel_TL1080P_night",
            "Fisheye_STM720P", "Fisheye_STM720P_daytime_no_vehicle",
            "Fisheye_STM720P_daytime_vehicle", "Fisheye_STM720P_night_no_vehicle",
            "Fisheye_STM720P_night_vehicle"
        ]

        return alg_name in algorithm_matches and alarmtype == 1

    def copy_videos(self, videos, src_dir, dst_dir):
        """拷贝视频到指定目录"""
        if not os.path.exists(dst_dir):
            os.makedirs(dst_dir)
        for video_name in videos:
            src_path = os.path.join(src_dir, video_name)
            dst_path = os.path.join(dst_dir, video_name)
            if os.path.exists(src_path):
                shutil.copy2(str(src_path), str(dst_path))
                self.log_message(f"复制 {src_path} 到 {dst_path}")
            else:
                self.log_message(f"源文件不存在: {src_path}")

    def process_videos(self, result_file, annotation_dir, dst_dir, output_dir_path, test_mode, print_FN_FP, split_result, copy_video):
        """主处理函数"""
        if test_mode == "BSD":
            alarmtype_to_alg_name = self.alarmtype_to_alg_name_bsd
        else:
            self.log_message("无效的测试模式！")
            return

        # alg_name 到 alarmtype 的映射关系（反向映射）
        alg_name_to_alarmtype = {v: k for k, v in alarmtype_to_alg_name.items()}

        # 提取信息
        video_alarm_map_before = self.extract_alarm_info_before(result_file)
        video_annotations, video_paths = self.extract_annotation_info(annotation_dir, alg_name_to_alarmtype, video_alarm_map_before, test_mode)
        video_alarm_map = self.extract_alarm_info(result_file, video_annotations)

        self.log_message(f"*****未校对视频数：{len(video_alarm_map) - len(video_annotations)}")
        self.log_message("*" * 100)

        # 拆分结果文件
        if split_result and output_dir_path:
            self.split_and_merge_result_file(result_file, video_annotations, output_dir_path, video_paths, test_mode)

        self.log_message("*" * 100)

        # 初始化统计字典（完全按照原脚本）
        positive_errors = defaultdict(lambda: defaultdict(int))
        negative_errors = defaultdict(lambda: defaultdict(int))
        positive_correct = defaultdict(int)
        negative_correct = defaultdict(int)
        attribute_positive_errors = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        attribute_negative_errors = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        positive_total_attribute_value = defaultdict(int)
        negative_total_attribute_value = defaultdict(int)

        positive_attribute_value = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
        negative_attribute_value = defaultdict(lambda: defaultdict(lambda: defaultdict(int)))

        # 统计每种 alg_name 的正样本总数和负样本总数
        total_positive_samples = defaultdict(int)
        total_negative_samples = defaultdict(int)

        # 进行统计分析（完全按照原脚本逻辑）
        for video_name, alarms in video_alarm_map.items():
            if video_name in video_annotations:
                annotation = video_annotations[video_name]
                sample_type = annotation.get("sample_type")
                alg_name = annotation.get("alg_name")
                alarmtype = annotation.get("alarmtype")

                if sample_type == "positive":
                    total_positive_samples[alg_name] += 1
                    if alarmtype in alarms:
                        positive_correct[alg_name] += 1
                    else:
                        positive_errors[alg_name][alarmtype_to_alg_name.get(alarmtype, "unknown")] += 1
                elif sample_type == "negative":
                    total_negative_samples[alg_name] += 1
                    if alarmtype not in alarms:
                        negative_correct[alg_name] += 1
                    else:
                        negative_errors[alg_name][alarmtype_to_alg_name.get(alarmtype, "unknown")] += 1

                # 属性统计（只处理 part_attr）
                if alg_name in self.attributes:
                    part_attrs = self.attributes[alg_name]["part_attr"]
                    for attr in part_attrs:
                        value = annotation.get(attr)
                        if value is not None:
                            if isinstance(value, list):
                                attribute_value = ','.join(map(str, value))
                            else:
                                attribute_value = value

                            if sample_type == "positive":
                                positive_total_attribute_value[attribute_value] += 1
                                positive_attribute_value[alg_name][attr][attribute_value] += 1
                                if alarmtype not in alarms:
                                    attribute_positive_errors[alg_name][attr][attribute_value] += 1
                            elif sample_type == "negative":
                                negative_total_attribute_value[attribute_value] += 1
                                negative_attribute_value[alg_name][attr][attribute_value] += 1
                                if alarmtype in alarms:
                                    attribute_negative_errors[alg_name][attr][attribute_value] += 1

        # 拷贝错误视频
        if copy_video and dst_dir:
            self.copy_error_videos(video_annotations, video_alarm_map, video_paths, dst_dir, alarmtype_to_alg_name)

        # 打印统计结果
        self.print_statistics(total_positive_samples, total_negative_samples, positive_correct, negative_correct,
                            positive_errors, negative_errors, attribute_positive_errors, attribute_negative_errors,
                            positive_attribute_value, negative_attribute_value, print_FN_FP)

    def copy_error_videos(self, video_annotations, video_alarm_map, video_paths, dst_dir, alarmtype_to_alg_name):
        """拷贝错误检测的视频"""
        positive_error_dir = os.path.join(dst_dir, '漏检视频')
        negative_error_dir = os.path.join(dst_dir, '误检视频')

        # 处理正样本漏检
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_type") == "positive" and video_name in video_alarm_map:
                alarms = video_alarm_map[video_name]
                alarmtype = annotation.get("alarmtype")
                if alarmtype not in alarms:
                    error_dir = os.path.join(positive_error_dir, "BSD_" + alarmtype_to_alg_name.get(alarmtype, "unknown"))
                    os.makedirs(error_dir, exist_ok=True)
                    src_path = video_paths.get(video_name)
                    if src_path:
                        self.copy_videos([video_name], os.path.dirname(src_path), error_dir)

        # 处理负样本误检
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_type") == "negative" and video_name in video_alarm_map:
                alarms = video_alarm_map[video_name]
                alarmtype = annotation.get("alarmtype")
                if alarmtype in alarms:
                    error_dir = os.path.join(negative_error_dir, "BSD_" + alarmtype_to_alg_name.get(alarmtype, "unknown"))
                    os.makedirs(error_dir, exist_ok=True)
                    src_path = video_paths.get(video_name)
                    if src_path:
                        self.copy_videos([video_name], os.path.dirname(src_path), error_dir)

    def print_statistics(self, total_positive_samples, total_negative_samples, positive_correct, negative_correct,
                        positive_errors, negative_errors, attribute_positive_errors, attribute_negative_errors,
                        positive_attribute_value, negative_attribute_value, print_FN_FP):
        """打印统计信息（完全按照原脚本逻辑）"""
        self.log_message("*" * 60 + "每种算法的正样本总数和负样本总数" + "*" * 60)
        header = f"{'算法':<40} {'正样本总数':<10} {'正样本漏检数':<10} {'正样本正检数':<10} {'召回率':<10} {'负样本总数':<10} {'误检总数':<10} {'精确率':<10}"
        self.log_message(header)

        for alg_name in self.alg_name_sorted:
            # 只有当总的正负样本数都大于0时才填值
            total_positive = total_positive_samples.get(alg_name, 0)
            total_negative = total_negative_samples.get(alg_name, 0)
            if total_positive > 0 or total_negative > 0:
                positive_errors_sum = sum(positive_errors[alg_name].values())
                negative_errors_sum = sum(negative_errors[alg_name].values())
                positive_correct_count = positive_correct[alg_name]

                if total_positive_samples[alg_name] != 0 and (positive_correct[alg_name] + negative_errors_sum) != 0:
                    recall_rate = (positive_correct[alg_name] / total_positive_samples[alg_name]) * 100
                    precision_rate = positive_correct[alg_name] / (positive_correct[alg_name] + negative_errors_sum) * 100
                    stats_line = f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {positive_errors_sum:>10}\t{positive_correct[alg_name]:>15}\t {recall_rate:>12.2f}%\t{total_negative_samples[alg_name]:>10}\t {negative_errors_sum:>10}\t{precision_rate:>12.2f}%\t"
                elif total_positive_samples[alg_name] == 0 and (positive_correct[alg_name] + negative_errors_sum) != 0:
                    precision_rate = positive_correct[alg_name] / (positive_correct[alg_name] + negative_errors_sum) * 100
                    stats_line = f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {positive_errors_sum:>10}\t{positive_correct[alg_name]:>15}\t {'     ':>12}\t{total_negative_samples[alg_name]:>10}\t {negative_errors_sum:>10}{precision_rate:>12.2f}%\t"
                elif total_positive_samples[alg_name] != 0 and (positive_correct[alg_name] + negative_errors_sum) == 0:
                    recall_rate = (positive_correct[alg_name] / total_positive_samples[alg_name]) * 100
                    stats_line = f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {positive_errors_sum:>10}\t{positive_correct[alg_name]:>15}\t {recall_rate:>12.2f}%\t{total_negative_samples[alg_name]:>10}\t {negative_errors_sum:>10}{' ':>12}\t"
                elif total_positive_samples[alg_name] == 0 and (positive_correct[alg_name] + negative_errors_sum) == 0:
                    stats_line = f"{alg_name:<40}{total_positive_samples[alg_name]:>8}\t {positive_errors_sum:>10}\t{positive_correct[alg_name]:>15}\t {'       ':>12}\t{total_negative_samples[alg_name]:>10}\t {negative_errors_sum:>10}{' ':>12}\t"

                self.log_message(stats_line)

        # 打印详细属性统计
        if print_FN_FP:
            self.print_attribute_statistics(attribute_positive_errors, positive_attribute_value, "正样本漏检数", "召回率", "positive")
            self.print_attribute_statistics(attribute_negative_errors, negative_attribute_value, "负样本误检数", "精确率", "negative")

    def print_attribute_statistics(self, attribute_errors, attribute_value, error_type, rate_type, sample_type):
        """打印属性统计信息（完全按照原脚本逻辑）"""
        self.log_message("*" * 70 + f"每种属性值的{error_type}" + "*" * 70)

        for alg_name in self.alg_name_sorted:
            if alg_name in attribute_errors:
                self.log_message("*" * 43 + f"{alg_name:^20}" + "*" * 43)
                attrs = attribute_errors[alg_name]
                header = f"{'属性':^25} {'属性值':<35} {f'{sample_type}样本总数':>10} {error_type:>10} {f'{sample_type}样本正检数':>10} {rate_type:>10}"
                self.log_message(header)

                for attr in self.alg_name_sorted.get(alg_name, {}):
                    sorted_values = self.alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        self.log_message(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            if sample_type == "positive":
                                rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            else:
                                rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            self.log_message(f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {rate:>12.2f}%\t")
                    else:
                        self.log_message(f"{attr:^25}")
                        for value in sorted_values:
                            count = attrs[attr].get(value, 0)
                            count1 = attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            if sample_type == "positive":
                                rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            else:
                                rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            self.log_message(f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {rate:>12.2f}%\t")
            else:
                attrs = attribute_value[alg_name]
                should_print_header = True
                for attr1 in attribute_value.get(alg_name, {}):
                    for value1 in attribute_value[alg_name].get(attr1, {}):
                        count2 = attribute_value[alg_name][attr1][value1]
                        if should_print_header:
                            if count2 > 0:
                                self.log_message("*" * 43 + f"{alg_name:^20}" + "*" * 43)
                                header = f"{'属性':^25} {'属性值':<35} {f'{sample_type}样本总数':>10} {error_type:>10} {f'{sample_type}样本正检数':>10} {rate_type:>10}"
                                self.log_message(header)
                                should_print_header = False

                for attr in self.alg_name_sorted.get(alg_name, {}):
                    sorted_values = self.alg_name_sorted[alg_name].get(attr, [])
                    if attr in attrs:
                        self.log_message(f"{attr:^25}")
                        for value in sorted_values:
                            count = 0
                            count1 = attribute_value[alg_name][attr][value]
                            correct_count = count1 - count
                            if sample_type == "positive":
                                rate = ((correct_count / count1) * 100) if count1 > 0 else -1
                            else:
                                rate = ((correct_count / count1) * 100) if count1 > 0 else 0
                            self.log_message(f"{'':^25}\t {value:<35}\t {count1:>6}\t {count:>12}\t {correct_count:>11}\t {rate:>12.2f}%\t")

        self.log_message("*" * 100)

    def split_and_merge_result_file(self, result_file, video_annotations, output_dir, video_paths, test_mode):
        """拆分和合并结果文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        detailed_files = defaultdict(lambda: defaultdict(list))

        with open(result_file, 'r') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError:
                    self.log_message(f"Invalid JSON format: {line}")
                    continue

                alarmtype_str = data.get("alarmtype", 0)

                try:
                    alarmtype = int(alarmtype_str)
                except ValueError:
                    alarmtype = -1

                filepath = data.get("filepath")
                video_name_old, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_name_old + ext

                # 更新文件路径
                for video_src_name, src_path in video_paths.items():
                    if video_name == video_src_name:
                        data['filepath'] = os.path.normpath(src_path)

                # 检查算法匹配并更新alarmtype（完全按照原脚本逻辑）
                for video_annotations_name, video_annotations_attr in video_annotations.items():
                    if video_name == video_annotations_name:
                        alg_name = video_annotations[video_name]['alg_name']
                        alarmtype_annotations = video_annotations[video_name]['alarmtype']
                        if ((alg_name == "Overlook_STM720P_daytime" and alarmtype == 1) or
                            (alg_name == "Overlook_STM720P_night" and alarmtype == 1) or
                            (alg_name == "Overlook_TL1080P_daytime" and alarmtype == 1) or
                            (alg_name == "Overlook_TL1080P_night" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_daytime_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_STM720P_night_vehicle" and alarmtype == 1) or
                            (alg_name == "Parallel_TL1080P_daytime" and alarmtype == 1) or
                            (alg_name == "Parallel_TL1080P_night" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_daytime_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_daytime_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_night_no_vehicle" and alarmtype == 1) or
                            (alg_name == "Fisheye_STM720P_night_vehicle" and alarmtype == 1)):
                            alarmtype = alarmtype_annotations

                data["alarmtype"] = alarmtype

                if video_name in video_annotations:
                    annotation = video_annotations[video_name]
                    expected_alarmtype = annotation.get("alarmtype", 0)
                    alg_name = annotation.get("alg_name", "unknown")
                    sample_type = annotation.get("sample_type", "unknown")

                    if test_mode == "BSD":
                        detailed_filename = f"{test_mode}_{alg_name}_{sample_type}.txt"
                    else:
                        detailed_filename = f"{test_mode}_{expected_alarmtype}_{alg_name}_{sample_type}.txt"

                    if data.get("alarmtype") == expected_alarmtype or data.get("alarmtype") == 0:
                        detailed_files[detailed_filename][video_name].append(data)
                    elif data.get("alarmtype") != expected_alarmtype:
                        empty_result = {
                            "filepath": data.get("filepath"),
                            "alarmTime": []
                        }
                        detailed_files[detailed_filename][video_name].append(empty_result)

        # 写入拆分的文件
        if len(detailed_files) > 0:
            self.log_message(f"{'拆分结果':<20} {'路径':<50}")

        for filename, videos_data in detailed_files.items():
            output_file = os.path.join(output_dir, filename)
            self.log_message(f"{filename:<20} {output_file:<50}")

            with open(output_file, 'w') as file:
                for video_name, entries in videos_data.items():
                    merged_result = {"filepath": next(entry["filepath"] for entry in entries)}
                    alarm_times = [entry.get("alarmTime") for entry in entries if "alarmTime" in entry]
                    merged_result["alarmTime"] = sorted(set(alarm_time for sublist in alarm_times for alarm_time in
                                                            (sublist if isinstance(sublist, list) else [sublist])))
                    file.write(json.dumps(merged_result, ensure_ascii=False) + "\n")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = BSDEffectTestUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
