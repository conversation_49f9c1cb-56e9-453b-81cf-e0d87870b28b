import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
from collections import defaultdict
import shutil
import configparser

class DMSEffectTestUI:
    def __init__(self, root):
        self.root = root
        self.root.title("DMS效果测试工具 v1.0")
        self.root.geometry("1200x900")
        
        # 配置文件路径
        self.config_file = "dms_config.ini"
        
        # 初始化变量
        self.init_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 加载配置（启动时不显示消息）
        self.load_config(show_message=False)
        
        # 初始化算法映射和属性
        self.init_algorithm_data()
    
    def init_variables(self):
        """初始化界面变量"""
        self.source_dir = tk.StringVar()
        self.result_file = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.destination_dir = tk.StringVar()
        self.test_mode = tk.StringVar(value="DMS")
        self.print_fn_fp = tk.BooleanVar(value=True)
        self.split_result = tk.BooleanVar(value=False)
        self.copy_video = tk.BooleanVar(value=False)
        self.export_excel = tk.BooleanVar(value=False)
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 路径配置区域
        self.create_path_section(main_frame, 0)
        
        # 功能配置区域
        self.create_function_section(main_frame, 1)
        
        # 高级配置区域
        self.create_advanced_section(main_frame, 2)
        
        # 控制按钮区域
        self.create_control_section(main_frame, 3)
        
        # 日志显示区域
        self.create_log_section(main_frame, 4)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def create_path_section(self, parent, row):
        """创建路径配置区域"""
        path_frame = ttk.LabelFrame(parent, text="路径配置", padding="5")
        path_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        path_frame.columnconfigure(1, weight=1)
        
        # 源视频和标定目录
        ttk.Label(path_frame, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.source_dir, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.source_dir)).grid(row=0, column=2, padx=5)
        
        # 结果文件
        ttk.Label(path_frame, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.result_file, width=60).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_file(self.result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5)
        
        # 输出目录
        ttk.Label(path_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.output_dir, width=60).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.output_dir)).grid(row=2, column=2, padx=5)
        
        # 目标目录
        ttk.Label(path_frame, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5)
        ttk.Entry(path_frame, textvariable=self.destination_dir, width=60).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5)
        ttk.Button(path_frame, text="浏览", command=lambda: self.browse_directory(self.destination_dir)).grid(row=3, column=2, padx=5)
    
    def create_function_section(self, parent, row):
        """创建功能配置区域"""
        func_frame = ttk.LabelFrame(parent, text="功能配置", padding="5")
        func_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 测试模式
        ttk.Label(func_frame, text="测试模式:").grid(row=0, column=0, sticky=tk.W, padx=5)
        mode_combo = ttk.Combobox(func_frame, textvariable=self.test_mode, values=["DMS"], state="readonly", width=10)
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 功能开关
        ttk.Checkbutton(func_frame, text="打印漏误检详细分布", variable=self.print_fn_fp).grid(row=1, column=0, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拆分结果文件", variable=self.split_result).grid(row=1, column=1, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="拷贝漏误检视频", variable=self.copy_video).grid(row=1, column=2, sticky=tk.W, padx=5)
        ttk.Checkbutton(func_frame, text="导出Excel报告", variable=self.export_excel).grid(row=1, column=3, sticky=tk.W, padx=5)
    
    def create_advanced_section(self, parent, row):
        """创建高级配置区域"""
        adv_frame = ttk.LabelFrame(parent, text="高级配置", padding="5")
        adv_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(adv_frame, text="编辑报警映射", command=self.edit_alarm_mapping).grid(row=0, column=0, padx=5)
        ttk.Button(adv_frame, text="编辑属性定义", command=self.edit_attributes).grid(row=0, column=1, padx=5)
        ttk.Button(adv_frame, text="编辑排序规则", command=self.edit_sorting_rules).grid(row=0, column=2, padx=5)
        ttk.Button(adv_frame, text="查看场景说明", command=self.show_scene_info).grid(row=0, column=3, padx=5)
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        ctrl_frame = ttk.Frame(parent)
        ctrl_frame.grid(row=row, column=0, columnspan=3, pady=10)
        
        ttk.Button(ctrl_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="编辑配置", command=self.edit_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(ctrl_frame, text="重置配置", command=self.reset_config).pack(side=tk.LEFT, padx=5)
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def browse_directory(self, var):
        """浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)
    
    def browse_file(self, var, title, filetypes):
        """浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def init_algorithm_data(self):
        """初始化算法数据"""
        # DMS报警掩码到场景的映射关系
        self.alarmMask_to_material_scene = {
            1: "call",
            2: "smoke",
            4: "eyeclose",
            8: "yawn",
            16: "distract",
            32: "abnormal",
            64: "ir",
            128: "lens_block",
            256: "seatbelt",
            1024: "helmet",
            8192: "drink",
        }

        # DMS场景属性定义
        self.attributes = {
            "call": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "calling", "chirality", "place_hold_phone", "phone_orientation", "vehicle_vibrating", "phone_color", "calling_false_action")
            },
            "smoke": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "cigarette_pose", "chirality", "type_of_cigarette", "cigarettes_length", "cigarette_light", "butt_towards",
                            "cigarette_surface_brightness", "smoking_false_action")
            },
            "eyeclose": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "eyes_status", "eyes_makeup", "direction_of_face", "close_eye_false_action", "close_eye_special_action")
            },
            "yawn": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "yawn_degree", "direction_of_face", "corners_of_the_mouth_cover", "yawn_false_action")
            },
            "distract": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "face_angle", "watch_point", "distraction_false_action")
            },
            "abnormal": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "intravital_human", "abnormal_type", "abnormal_false_action")
            },
            "ir": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "infrared_false_action")
            },
            "lens_block": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "cover_items", "cover_area", "camera_distance", "cover_false_action")
            },
            "seatbelt": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "safetybelt_color", "safetybelt_coverarea", "clothing_type", "clothes_color", "safetybelt_false_action")
            },
            "helmet": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "hat", "cap_color", "cap_cover")
            },
            "drink": {
                "part_attr": ("camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
                            "cup_orientation", "cup_type", "cover_area_face", "cup_cover", "hold_cup_hand", "cup_color")
            },
        }

        # 场景排序规则（简化版本，完整版本包含所有属性值）
        self.material_scene_sorted = {
            'call': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'calling': ['calling_ear_left', 'calling_ear_right', 'calling_mouth'],
                'chirality': ['lefthand', 'righthand'],
                'phone_color': ['dark_color', 'light_color'],
            },
            'smoke': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'cigarette_pose': ['smoking_jia_two_fingers', 'smoking_jia_multiple_fingers', 'smoking_pitch_front', 'smoking_pitch_back'],
                'type_of_cigarette': ['thick', 'thin'],
                'cigarette_light': ['yes', 'no'],
            },
            'eyeclose': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'eyes_status': ['all_close', 'one_open_one_close', 'narrow_eyes'],
                'eyes_makeup': ['colored_contacts_or_smoky_eyes', 'long_eyelashes', 'no'],
            },
            'yawn': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'yawn_degree': ['yawn_very_small', 'yawn_small', 'yawn_middle', 'yawn_big'],
            },
            'distract': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'face_angle': ['left', 'right', 'rise', 'down'],
                'watch_point': ['m1', 'm2', 'm3'],
            },
            'abnormal': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'intravital_human': ['human', 'dummy'],
                'abnormal_type': ['unmanned', 'face_occlusion'],
            },
            'ir': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'infrared_false_action': ['myopia_glass', 'high_transmittanc_sunglasses', 'medium_transmittanc_sunglasses'],
            },
            'lens_block': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'cover_items': ['hand', 'palm', 'paper', 'fingers', 'clothes', 'towel'],
                'cover_area': ['percent85_to_percent90', 'greater_than_percent90', 'percent100'],
            },
            'seatbelt': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'safetybelt_color': ['dark_seatbelt', 'light_seatbelt'],
                'clothing_type': ['winter_clothes', 'summer_clothes'],
            },
            'helmet': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'hat': ['safe_helmet', 'peaked_cap', 'baseball_cap', 'knitted_hat', 'bareheaded'],
                'cap_color': ['yellow', 'blue', 'red', 'white', 'others'],
            },
            'drink': {
                'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror', 'bottom_center_of_the_windshield'],
                'cup_type': ['mineral_water_bottle', 'disposable_cup', 'vacuum_cup', 'ring_pull_can', 'other'],
                'cup_color': ['dark_color', 'light_color', 'white_color', 'other'],
            },
        }

    def edit_alarm_mapping(self):
        """编辑报警映射"""
        self.show_config_editor("报警映射配置", self.alarmMask_to_material_scene)

    def edit_attributes(self):
        """编辑属性定义"""
        self.show_config_editor("属性定义配置", self.attributes)

    def edit_sorting_rules(self):
        """编辑排序规则"""
        self.show_config_editor("排序规则配置", self.material_scene_sorted)

    def show_scene_info(self):
        """显示场景说明"""
        info_window = tk.Toplevel(self.root)
        info_window.title("DMS场景说明")
        info_window.geometry("800x600")

        # 创建文本显示区域
        text_area = scrolledtext.ScrolledText(info_window, wrap=tk.WORD)
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 插入场景说明内容
        scene_info = """
DMS（驾驶员监控系统）场景说明：

1. call (打电话) - 报警掩码: 1
   检测驾驶员是否在打电话，包括手持电话、免提通话等行为

2. smoke (抽烟) - 报警掩码: 2
   检测驾驶员是否在抽烟，包括点烟、吸烟等行为

3. eyeclose (闭眼) - 报警掩码: 4
   检测驾驶员是否闭眼或眯眼，用于疲劳驾驶检测

4. yawn (打哈欠) - 报警掩码: 8
   检测驾驶员打哈欠行为，疲劳驾驶的重要指标

5. distract (分心) - 报警掩码: 16
   检测驾驶员注意力分散，如看手机、看副驾驶等

6. abnormal (异常) - 报警掩码: 32
   检测异常情况，如无人驾驶、面部遮挡等

7. ir (红外) - 报警掩码: 64
   红外相关检测，如红外阻挡等

8. lens_block (镜头遮挡) - 报警掩码: 128
   检测摄像头镜头是否被遮挡

9. seatbelt (安全带) - 报警掩码: 256
   检测驾驶员是否系安全带

10. helmet (头盔) - 报警掩码: 1024
    检测驾驶员是否佩戴头盔（适用于摩托车等）

11. drink (喝水) - 报警掩码: 8192
    检测驾驶员喝水或饮用其他液体的行为

每个场景都包含多个属性维度：
- 基础属性：摄像头位置、眼镜类型、帽子、口罩、年龄、性别、时间、光照、天气
- 场景特定属性：根据不同场景有不同的专属属性
        """

        text_area.insert(tk.END, scene_info)
        text_area.config(state=tk.DISABLED)

    def show_config_editor(self, title, data):
        """显示配置编辑器"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title(title)
        editor_window.geometry("800x600")

        # 创建文本编辑器
        text_editor = scrolledtext.ScrolledText(editor_window, wrap=tk.WORD)
        text_editor.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 插入当前配置
        text_editor.insert(tk.END, json.dumps(data, indent=2, ensure_ascii=False))

        # 保存按钮
        def save_config():
            try:
                new_data = json.loads(text_editor.get(1.0, tk.END))
                if title == "报警映射配置":
                    self.alarmMask_to_material_scene = new_data
                elif title == "属性定义配置":
                    self.attributes = new_data
                elif title == "排序规则配置":
                    self.material_scene_sorted = new_data
                messagebox.showinfo("成功", "配置已保存")
                editor_window.destroy()
            except json.JSONDecodeError:
                messagebox.showerror("错误", "JSON格式错误，请检查配置")

        ttk.Button(editor_window, text="保存", command=save_config).pack(pady=5)

    def save_config(self):
        """保存配置到文件"""
        config = configparser.ConfigParser()
        config['PATHS'] = {
            'source_dir': self.source_dir.get(),
            'result_file': self.result_file.get(),
            'output_dir': self.output_dir.get(),
            'destination_dir': self.destination_dir.get()
        }
        config['SETTINGS'] = {
            'test_mode': self.test_mode.get(),
            'print_fn_fp': str(self.print_fn_fp.get()),
            'split_result': str(self.split_result.get()),
            'copy_video': str(self.copy_video.get()),
            'export_excel': str(self.export_excel.get())
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self, show_message=True):
        """从文件加载配置"""
        if not os.path.exists(self.config_file):
            if show_message:
                messagebox.showinfo("提示", f"配置文件 {self.config_file} 不存在")
            return

        config = configparser.ConfigParser()
        try:
            config.read(self.config_file, encoding='utf-8')

            if 'PATHS' in config:
                self.source_dir.set(config['PATHS'].get('source_dir', ''))
                self.result_file.set(config['PATHS'].get('result_file', ''))
                self.output_dir.set(config['PATHS'].get('output_dir', ''))
                self.destination_dir.set(config['PATHS'].get('destination_dir', ''))

            if 'SETTINGS' in config:
                self.test_mode.set(config['SETTINGS'].get('test_mode', 'DMS'))
                self.print_fn_fp.set(config['SETTINGS'].getboolean('print_fn_fp', True))
                self.split_result.set(config['SETTINGS'].getboolean('split_result', False))
                self.copy_video.set(config['SETTINGS'].getboolean('copy_video', False))
                self.export_excel.set(config['SETTINGS'].getboolean('export_excel', False))

            if show_message:
                messagebox.showinfo("成功", "配置已加载")
                self.log_message("配置文件加载成功")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def edit_config(self):
        """编辑配置界面"""
        config_window = tk.Toplevel(self.root)
        config_window.title("DMS配置编辑器")
        config_window.geometry("650x550")
        config_window.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(config_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 路径配置标签页
        path_frame = ttk.Frame(notebook, padding="10")
        notebook.add(path_frame, text="路径配置")

        # 功能配置标签页
        settings_frame = ttk.Frame(notebook, padding="10")
        notebook.add(settings_frame, text="功能配置")

        # 创建路径配置界面
        self.create_path_config_tab(path_frame)

        # 创建功能配置界面
        self.create_settings_config_tab(settings_frame)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 保存和取消按钮
        ttk.Button(button_frame, text="保存配置", command=lambda: self.save_config_from_editor(config_window)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用设置", command=self.apply_config_from_editor).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

    def create_path_config_tab(self, parent):
        """创建路径配置标签页"""
        # 源视频和标定目录
        ttk.Label(parent, text="源视频和标定目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_source_dir = tk.StringVar(value=self.source_dir.get())
        source_entry = ttk.Entry(parent, textvariable=self.edit_source_dir, width=50)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_source_dir)).grid(row=0, column=2, padx=5, pady=5)

        # 结果文件
        ttk.Label(parent, text="结果文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_result_file = tk.StringVar(value=self.result_file.get())
        result_entry = ttk.Entry(parent, textvariable=self.edit_result_file, width=50)
        result_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_file_for_edit(self.edit_result_file, "选择结果文件", [("文本文件", "*.txt")])).grid(row=1, column=2, padx=5, pady=5)

        # 输出目录
        ttk.Label(parent, text="输出目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_output_dir = tk.StringVar(value=self.output_dir.get())
        output_entry = ttk.Entry(parent, textvariable=self.edit_output_dir, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_output_dir)).grid(row=2, column=2, padx=5, pady=5)

        # 目标目录
        ttk.Label(parent, text="目标目录:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_destination_dir = tk.StringVar(value=self.destination_dir.get())
        dest_entry = ttk.Entry(parent, textvariable=self.edit_destination_dir, width=50)
        dest_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.browse_directory_for_edit(self.edit_destination_dir)).grid(row=3, column=2, padx=5, pady=5)

        # 配置列权重
        parent.columnconfigure(1, weight=1)

    def create_settings_config_tab(self, parent):
        """创建功能配置标签页"""
        # 测试模式
        ttk.Label(parent, text="测试模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.edit_test_mode = tk.StringVar(value=self.test_mode.get())
        mode_combo = ttk.Combobox(parent, textvariable=self.edit_test_mode, values=["DMS"], state="readonly", width=15)
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 功能开关
        self.edit_print_fn_fp = tk.BooleanVar(value=self.print_fn_fp.get())
        ttk.Checkbutton(parent, text="打印漏误检详细分布", variable=self.edit_print_fn_fp).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_split_result = tk.BooleanVar(value=self.split_result.get())
        ttk.Checkbutton(parent, text="拆分结果文件", variable=self.edit_split_result).grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_copy_video = tk.BooleanVar(value=self.copy_video.get())
        ttk.Checkbutton(parent, text="拷贝漏误检视频", variable=self.edit_copy_video).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        self.edit_export_excel = tk.BooleanVar(value=self.export_excel.get())
        ttk.Checkbutton(parent, text="导出Excel报告", variable=self.edit_export_excel).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # 说明文本
        info_text = tk.Text(parent, height=8, width=60, wrap=tk.WORD)
        info_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=10)

        info_content = """DMS配置说明：

• 打印漏误检详细分布：启用后会显示每种属性值的详细统计分析
• 拆分结果文件：将结果按场景和样本类型拆分保存到不同文件
• 拷贝漏误检视频：将检测错误的视频分类拷贝到指定目录
• 导出Excel报告：生成Excel格式的详细统计报告

DMS支持11种检测场景：
call(打电话)、smoke(抽烟)、eyeclose(闭眼)、yawn(打哈欠)、
distract(分心)、abnormal(异常)、ir(红外)、lens_block(镜头遮挡)、
seatbelt(安全带)、helmet(头盔)、drink(喝水)

路径配置：
• 源视频和标定目录：包含测试视频和对应标定文件的目录
• 结果文件：算法输出的检测结果文件（JSON Lines格式，包含alarmMask字段）
• 输出目录：拆分结果文件的保存位置
• 目标目录：错误视频的分类保存位置"""

        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # 配置行列权重
        parent.columnconfigure(1, weight=1)
        parent.rowconfigure(5, weight=1)

    def browse_directory_for_edit(self, var):
        """为编辑器浏览目录"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)

    def browse_file_for_edit(self, var, title, filetypes):
        """为编辑器浏览文件"""
        filename = filedialog.askopenfilename(title=title, filetypes=filetypes)
        if filename:
            var.set(filename)

    def apply_config_from_editor(self):
        """从编辑器应用配置到主界面"""
        self.source_dir.set(self.edit_source_dir.get())
        self.result_file.set(self.edit_result_file.get())
        self.output_dir.set(self.edit_output_dir.get())
        self.destination_dir.set(self.edit_destination_dir.get())
        self.test_mode.set(self.edit_test_mode.get())
        self.print_fn_fp.set(self.edit_print_fn_fp.get())
        self.split_result.set(self.edit_split_result.get())
        self.copy_video.set(self.edit_copy_video.get())
        self.export_excel.set(self.edit_export_excel.get())

        self.log_message("配置已应用到主界面")
        messagebox.showinfo("成功", "配置已应用到主界面")

    def save_config_from_editor(self, window):
        """从编辑器保存配置"""
        # 先应用到主界面
        self.apply_config_from_editor()
        # 然后保存配置
        self.save_config()
        # 关闭编辑器窗口
        window.destroy()

    def reset_config(self):
        """重置配置"""
        self.source_dir.set('')
        self.result_file.set('')
        self.output_dir.set('')
        self.destination_dir.set('')
        self.test_mode.set('DMS')
        self.print_fn_fp.set(True)
        self.split_result.set(False)
        self.copy_video.set(False)
        self.export_excel.set(False)
        self.log_text.delete(1.0, tk.END)

    def validate_inputs(self):
        """验证输入参数"""
        if not self.source_dir.get():
            messagebox.showerror("错误", "请选择源视频和标定目录")
            return False
        if not self.result_file.get():
            messagebox.showerror("错误", "请选择结果文件")
            return False
        if not os.path.exists(self.source_dir.get()):
            messagebox.showerror("错误", "源目录不存在")
            return False
        if not os.path.exists(self.result_file.get()):
            messagebox.showerror("错误", "结果文件不存在")
            return False
        return True

    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        # 在新线程中运行处理逻辑
        self.progress.start()
        thread = threading.Thread(target=self.run_processing)
        thread.daemon = True
        thread.start()

    def run_processing(self):
        """运行处理逻辑"""
        try:
            self.log_message("开始处理DMS数据...")

            # 调用原始处理函数
            self.process_videos(
                self.result_file.get(),
                self.source_dir.get(),
                self.destination_dir.get(),
                self.output_dir.get(),
                self.test_mode.get(),
                self.print_fn_fp.get(),
                self.split_result.get(),
                self.copy_video.get()
            )

            self.log_message("处理完成！")

        except Exception as e:
            self.log_message(f"处理出错: {str(e)}")
        finally:
            self.progress.stop()

    # 以下是原始脚本的核心处理函数，集成到UI类中
    def extract_alarm_info_before(self, result_file):
        """根据结果文件内容提取视频和报警类型映射"""
        extract_alarm_info_before = {}

        with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError:
                    self.log_message(f"Invalid JSON format: {line}")
                    continue

                alarmMask_str = data.get("alarmMask", "0")

                try:
                    alarmMask = int(alarmMask_str)
                except ValueError:
                    alarmMask = -1

                filepath = data.get("filepath")
                video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_pro_name + ext

                if video_name not in extract_alarm_info_before:
                    extract_alarm_info_before[video_name] = set()
                extract_alarm_info_before[video_name].add(alarmMask)

        self.log_message(f"总共提取了 {len(extract_alarm_info_before)} 个视频结果。")
        return extract_alarm_info_before

    def extract_annotation_info(self, annotation_dir, material_scene_to_alarmMask, extract_alarm_info_before, test_mode):
        """根据标定文件提取视频的标定信息"""
        video_annotations = {}
        video_file_paths = {}

        def handle_regular_annotation(video_name, first_annotation_data, material_scene, root, alarmMask):
            video_annotation = {
                # 通用属性
                "camera_pos": first_annotation_data.get("camera_pos"),
                "glass_type": first_annotation_data.get("glass_type"),
                "is_hat": first_annotation_data.get("is_hat"),
                "mask": first_annotation_data.get("mask"),
                "agegroup": first_annotation_data.get("agegroup"),
                "gender": first_annotation_data.get("gender"),
                "time": first_annotation_data.get("time"),
                "mix_light": first_annotation_data.get("mix_light"),
                "weather": first_annotation_data.get("weather"),

                # 打电话相关属性
                "calling": first_annotation_data.get("calling"),
                "chirality": first_annotation_data.get("chirality"),
                "place_hold_phone": first_annotation_data.get("place_hold_phone"),
                "phone_orientation": first_annotation_data.get("phone_orientation"),
                "vehicle_vibrating": first_annotation_data.get("vehicle_vibrating"),
                "phone_color": first_annotation_data.get("phone_color"),
                "calling_false_action": first_annotation_data.get("calling_false_action"),

                # 抽烟相关属性
                "cigarette_pose": first_annotation_data.get("cigarette_pose"),
                "type_of_cigarette": first_annotation_data.get("type_of_cigarette"),
                "cigarettes_length": first_annotation_data.get("cigarettes_length"),
                "cigarette_light": first_annotation_data.get("cigarette_light"),
                "butt_towards": first_annotation_data.get("butt_towards"),
                "cigarette_surface_brightness": first_annotation_data.get("cigarette_surface_brightness"),
                "smoking_false_action": first_annotation_data.get("smoking_false_action"),

                # 闭眼相关属性
                "eyes_status": first_annotation_data.get("eyes_status"),
                "eyes_makeup": first_annotation_data.get("eyes_makeup"),
                "direction_of_face": first_annotation_data.get("direction_of_face"),
                "close_eye_false_action": first_annotation_data.get("close_eye_false_action"),
                "close_eye_special_action": first_annotation_data.get("close_eye_special_action"),

                # 打哈欠相关属性
                "yawn_degree": first_annotation_data.get("yawn_degree"),
                "corners_of_the_mouth_cover": first_annotation_data.get("corners_of_the_mouth_cover"),
                "yawn_false_action": first_annotation_data.get("yawn_false_action"),

                # 红外相关属性
                "infrared_false_action": first_annotation_data.get("infrared_false_action"),

                # 分心相关属性
                "face_angle": first_annotation_data.get("face_angle"),
                "watch_point": first_annotation_data.get("watch_point"),
                "distraction_false_action": first_annotation_data.get("distraction_false_action"),

                # 安全带相关属性
                "safetybelt_color": first_annotation_data.get("safetybelt_color"),
                "safetybelt_coverarea": first_annotation_data.get("safetybelt_coverarea"),
                "clothing_type": first_annotation_data.get("clothing_type"),
                "clothes_color": first_annotation_data.get("clothes_color"),
                "safetybelt_false_action": first_annotation_data.get("safetybelt_false_action"),

                # 异常相关属性
                "intravital_human": first_annotation_data.get("intravital_human"),
                "abnormal_type": first_annotation_data.get("abnormal_type"),
                "abnormal_false_action": first_annotation_data.get("abnormal_false_action"),

                # 镜头遮挡相关属性
                "cover_items": first_annotation_data.get("cover_items"),
                "cover_area": first_annotation_data.get("cover_area"),
                "camera_distance": first_annotation_data.get("camera_distance"),
                "cover_false_action": first_annotation_data.get("cover_false_action"),

                # 头盔相关属性
                "hat": first_annotation_data.get("hat"),
                "cap_color": first_annotation_data.get("cap_color"),
                "cap_cover": first_annotation_data.get("cap_cover"),

                # 喝水相关属性
                "cup_orientation": first_annotation_data.get("cup_orientation"),
                "cup_type": first_annotation_data.get("cup_type"),
                "cover_area_face": first_annotation_data.get("cover_area_face"),
                "cup_cover": first_annotation_data.get("cup_cover"),
                "hold_cup_hand": first_annotation_data.get("hold_cup_hand"),
                "cup_color": first_annotation_data.get("cup_color"),

                "sample_np_type": first_annotation_data.get("sample_np_type"),
                "material_scene": first_annotation_data.get("material_scene"),
                "alarmMask": alarmMask
            }
            video_annotations[video_name] = video_annotation
            video_file_paths[video_name] = os.path.join(root, video_name)

        # 遍历标定文件夹
        for root, dirs, files in os.walk(annotation_dir):
            for filename in files:
                if not filename.endswith('.txt') and filename not in extract_alarm_info_before:
                    continue

                if filename in extract_alarm_info_before:
                    video_pro_name, ext = os.path.splitext(os.path.basename(filename))
                    video_alarm_txt_name = video_pro_name + '.txt'
                    txt_file_path = os.path.join(root, video_alarm_txt_name)

                    try:
                        with open(txt_file_path, 'r', errors='ignore') as file:
                            lines = file.readlines()
                            if not lines:
                                continue

                            first_annotation_data = json.loads(lines[0])
                            material_scene = first_annotation_data.get("material_scene")
                            alarmMask = material_scene_to_alarmMask.get(material_scene, 0)

                            handle_regular_annotation(filename, first_annotation_data, material_scene, root, alarmMask)

                    except FileNotFoundError:
                        self.log_message(f"标注文件不存在: {txt_file_path}")
                        raise
                    except json.JSONDecodeError as e:
                        self.log_message(f"Error decoding JSON in file: {txt_file_path} - Error: {e}")
                        raise

        self.log_message(f"提取 {len(video_annotations)} 个视频标定结果。")
        return video_annotations, video_file_paths

    def extract_alarm_info(self, result_file, video_annotations, test_mode):
        """根据结果文件内容提取视频和报警类型映射"""
        video_alarm_map = {}

        with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError as e:
                    self.log_message(f"Invalid JSON format: {line}, Error: {e}")
                    raise

                alarmMask_str = data.get("alarmMask", "0")

                try:
                    alarmMask = int(alarmMask_str)
                except ValueError:
                    alarmMask = -1

                filepath = data.get("filepath")
                video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_pro_name + ext

                if video_name in video_annotations:
                    video_alarm_map.setdefault(video_name, set()).add(alarmMask)

        return video_alarm_map

    def process_videos(self, result_file, annotation_dir, dst_dir, output_dir_path, test_mode, print_FN_FP, split_result, copy_video):
        """主处理函数"""
        # 初始化映射关系和基础数据
        material_scene_to_alarmMask = {v: k for k, v in self.alarmMask_to_material_scene.items()}

        # 数据提取阶段
        video_alarm_map_before = self.extract_alarm_info_before(result_file)
        video_annotations, video_paths = self.extract_annotation_info(annotation_dir, material_scene_to_alarmMask, video_alarm_map_before, test_mode)
        video_alarm_map = self.extract_alarm_info(result_file, video_annotations, test_mode)

        # 文件处理阶段
        self.log_message(f"未校对视频数：{len(video_alarm_map) - len(video_annotations)}")
        self.log_message("=" * 100)

        if split_result and output_dir_path:
            self.split_and_merge_result_file(result_file, video_annotations, output_dir_path, video_paths, test_mode)

        # 初始化统计数据结构
        stats = self.initialize_statistics()

        # 核心统计逻辑
        self.process_statistics(video_alarm_map, video_annotations, stats)

        # 视频拷贝处理
        if copy_video and dst_dir:
            self.handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir)

        # 结果输出
        self.print_statistics(stats, print_FN_FP)

        return stats

    def initialize_statistics(self):
        """初始化统计数据结构"""
        return {
            'positive': defaultdict(lambda: defaultdict(int)),
            'negative': defaultdict(lambda: defaultdict(int)),
            'positive_correct': defaultdict(int),
            'negative_correct': defaultdict(int),
            'attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
            'attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
            'total_attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
            'total_attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
            'total_positive': defaultdict(int),
            'total_negative': defaultdict(int),
            'material_scene_sorted': self.material_scene_sorted
        }

    def process_statistics(self, video_alarm_map, video_annotations, stats):
        """处理核心统计逻辑"""
        for video_name, alarms in video_alarm_map.items():
            if video_name not in video_annotations:
                self.log_message(f"{video_name} 存在结果文件，缺失标注文件")
                continue

            annotation = video_annotations[video_name]
            material_scene = annotation.get("material_scene")
            sample_np_type = annotation.get("sample_np_type")
            alarmMask = annotation.get("alarmMask")

            # 基础样本统计
            self.update_base_stats(stats, material_scene, sample_np_type, alarms, alarmMask)

            # 属性级统计
            if material_scene in self.attributes:
                self.update_attribute_stats(stats, material_scene, sample_np_type, annotation, alarms, alarmMask)

    def update_base_stats(self, stats, material_scene, sample_np_type, alarms, alarmMask):
        """更新基础样本统计"""
        if sample_np_type == "positive":
            stats['total_positive'][material_scene] += 1
            if alarmMask in alarms:
                stats['positive_correct'][material_scene] += 1
            else:
                stats['positive'][material_scene]['total'] += 1
        elif sample_np_type == "negative":
            stats['total_negative'][material_scene] += 1
            if alarmMask not in alarms:
                stats['negative_correct'][material_scene] += 1
            else:
                stats['negative'][material_scene]['total'] += 1

    def update_attribute_stats(self, stats, material_scene, sample_np_type, annotation, alarms, alarmMask):
        """更新属性级统计"""
        for attr in self.attributes[material_scene]["part_attr"]:
            value = annotation.get(attr)
            if not value:
                continue

            value_str = ','.join(map(str, value)) if isinstance(value, list) else value
            key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

            # 更新总数
            stats[f'total_{key.split("_")[1]}'][value_str] += 1

            # 更新各属性值总数
            if sample_np_type in ["positive", "negative"]:
                stats[f'total_{key}'][material_scene][attr][value_str] += 1

            # 更新错误计数
            if (sample_np_type == "positive" and alarmMask not in alarms) or \
               (sample_np_type == "negative" and alarmMask in alarms):
                stats[key][material_scene][attr][value_str] += 1

    def handle_video_copy(self, video_annotations, video_alarm_map, video_paths, dst_dir):
        """处理视频拷贝逻辑"""
        def copy_files(sample_np_type, error_type):
            process_files = []
            for video_name, annotation in video_annotations.items():
                if annotation.get("sample_np_type") == sample_np_type:
                    if video_name in video_alarm_map:
                        alarms = video_alarm_map[video_name]
                        alarmMask = annotation.get("alarmMask")
                        condition = (alarmMask not in alarms) if sample_np_type == "positive" else (alarmMask in alarms)
                        if condition:
                            process_files.append(video_name)

            self.log_message(f"开始拷贝{error_type}，共{len(process_files)}个文件")
            for i, video_name in enumerate(process_files):
                if src_path := video_paths.get(video_name):
                    alarmMask = video_annotations[video_name].get("alarmMask")
                    error_dir = os.path.join(
                        dst_dir,
                        error_type,
                        f"DMS_{self.alarmMask_to_material_scene.get(alarmMask, 'unknown')}"
                    )
                    os.makedirs(error_dir, exist_ok=True)

                    # 拷贝视频文件
                    shutil.copy2(src_path, error_dir)
                    self.log_message(f"拷贝视频 {src_path} 到 {error_dir}")

                    # 拷贝标签文件
                    video_pro_name, ext = os.path.splitext(os.path.basename(video_name))
                    txt_file_name = video_pro_name + '.txt'
                    txt_src_path = os.path.join(os.path.dirname(src_path), txt_file_name)
                    if os.path.exists(txt_src_path):
                        shutil.copy2(txt_src_path, error_dir)
                        self.log_message(f"拷贝标签 {txt_src_path} 到 {error_dir}")

                # 更新进度
                progress = (i + 1) / len(process_files) * 100
                self.log_message(f"{error_type}拷贝进度: {progress:.1f}%")

        self.log_message("开始视频拷贝...")
        copy_files("positive", "漏检视频")
        copy_files("negative", "误检视频")
        self.log_message("视频拷贝完成")

    def print_statistics(self, stats, print_details):
        """打印统计结果"""
        self.log_message("每种算法的正样本总数和负样本总数")
        self.log_message("-" * 120)

        header = f"{'算法':<20} {'正样本总数':<10} {'正样本漏检数':<12} {'正样本正检数':<12} {'召回率':<10} {'负样本总数':<10} {'误检总数':<10} {'精准率':<10}"
        self.log_message(header)

        for material_scene in self.material_scene_sorted:
            tp = stats['total_positive'][material_scene]  # 正样本总数
            fp = stats['negative'][material_scene]['total']  # 误检数
            tn = stats['total_negative'][material_scene]  # 负样本总数
            fn = stats['positive'][material_scene]['total']  # 漏检数
            correct_p = stats['positive_correct'][material_scene]  # 正样本正检数
            correct_n = stats['negative_correct'][material_scene]  # 负样本正检数

            recall = self.safe_division(correct_p, tp) * 100
            precision = self.safe_division(correct_p, (correct_p + fp)) * 100

            if (tp or tn) > 0:
                stats_line = f"{material_scene:<20} {tp:<10} {fn:<12} {correct_p:<12} {recall:<10.2f}% {tn:<10} {fp:<10} {precision:<10.2f}%"
                self.log_message(stats_line)

        # 打印详细属性统计
        if print_details:
            self.print_attribute_stats(stats, "positive", "漏检")
            self.print_attribute_stats(stats, "negative", "误检")

    def print_attribute_stats(self, stats, sample_np_type, error_type):
        """打印属性级统计"""
        self.log_message(f"\n每种属性值的{error_type}数")
        self.log_message("-" * 120)

        rate_name = "召回率" if sample_np_type == "positive" else "误检率"
        sample_np_type_name = "正样本" if sample_np_type == "positive" else "负样本"
        key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

        for material_scene in self.material_scene_sorted:
            if material_scene not in stats[key]:
                continue

            self.log_message(f"\n{material_scene} 场景:")
            header = f"{'属性':<25} {'属性值':<35} {f'{sample_np_type_name}总数':<15} {f'{error_type}数':<10} {'正检数':<10} {rate_name:<10}"
            self.log_message(header)

            for attr in self.material_scene_sorted[material_scene]:
                if attr not in stats[key][material_scene]:
                    continue

                self.log_message(f"{attr}:")
                for value in self.material_scene_sorted[material_scene][attr]:
                    errors = stats[key][material_scene][attr].get(value, 0)
                    total = stats[f'total_{key}'][material_scene][attr].get(value, 0)
                    correct = total - errors

                    if sample_np_type == "positive":
                        rate = self.safe_division(correct, total) * 100
                    else:
                        rate = self.safe_division(errors, total) * 100

                    if total > 0:
                        stats_line = f"  {'':<23} {value:<35} {total:<15} {errors:<10} {correct:<10} {rate:<10.2f}%"
                        self.log_message(stats_line)

    def safe_division(self, numerator, denominator):
        """安全除法"""
        return numerator / denominator if denominator else 0

    def split_and_merge_result_file(self, result_file, video_annotations, output_dir, video_paths, test_mode):
        """拆分和合并结果文件"""
        os.makedirs(output_dir, exist_ok=True)
        detailed_files = defaultdict(lambda: defaultdict(list))

        with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                except json.JSONDecodeError as e:
                    self.log_message(f"Invalid JSON format: {line}")
                    raise e

                alarmMask = int(data.get("alarmMask", 0))
                filepath = data.get("filepath")
                video_name_old, ext = os.path.splitext(os.path.basename(filepath))
                video_name = video_name_old + ext

                # 更新文件路径
                for video_src_name, src_path in video_paths.items():
                    if video_name == video_src_name:
                        data['filepath'] = os.path.normpath(src_path)

                data["alarmMask"] = alarmMask

                if video_name in video_annotations:
                    annotation = video_annotations[video_name]
                    expected_alarmMask = annotation.get("alarmMask", 0)
                    material_scene = annotation.get("material_scene", "unknown")
                    sample_np_type = annotation.get("sample_np_type", "unknown")

                    filename = f"DMS_{expected_alarmMask}_{material_scene}_{sample_np_type}.txt"

                    if data.get("alarmMask") == expected_alarmMask:
                        detailed_files[filename][video_name].append(data)
                    else:
                        empty_result = {
                            "filepath": data.get("filepath"),
                            "alarmTime": []
                        }
                        detailed_files[filename][video_name].append(empty_result)

        # 写入拆分的文件
        if detailed_files:
            self.log_message(f"{'拆分结果':<30} {'路径':<50}")

        for filename, videos_data in detailed_files.items():
            output_file = os.path.join(output_dir, filename)
            self.log_message(f"{filename:<30} {output_file:<50}")

            with open(output_file, 'w') as file:
                for video_name, entries in videos_data.items():
                    merged_result = {"filepath": next(entry["filepath"] for entry in entries)}
                    alarm_times = [entry.get("alarmTime") for entry in entries if "alarmTime" in entry]
                    merged_result["alarmTime"] = sorted(set(alarm_time for sublist in alarm_times for alarm_time in
                                                            (sublist if isinstance(sublist, list) else [sublist])))
                    file.write(json.dumps(merged_result, ensure_ascii=False) + "\n")

        self.log_message("结果文件拆分并合并完毕。")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = DMSEffectTestUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
