from collections import defaultdict
import json
import os
import shutil
from tqdm import tqdm
import openpyxl
from openpyxl.styles import Border, Side

# alarmMask 到 material_scene 的映射关系
alarmMask_to_material_scene = {
    1: "call",
    2: "smoke",
    4: "eyeclose",
    8: "yawn",
    16: "distract",
    32: "abnormal",
    64: "ir",
    128: "lens_block",
    256: "seatbelt",
    1024: "helmet",
    8192: "drink",
}

# 每种算法的属性定义
attributes = {
    "call": {
        "part_attr": (
        "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
        "calling", "chirality", "place_hold_phone", "phone_orientation", "vehicle_vibrating", "phone_color",
        "calling_false_action")
    },

    "smoke": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "cigarette_pose", "chirality", "type_of_cigarette", "cigarettes_length", "cigarette_light", "butt_towards",
            "cigarette_surface_brightness", "smoking_false_action")
    },

    "eyeclose": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "eyes_status", "eyes_makeup", "direction_of_face", "close_eye_false_action", "close_eye_special_action")
    },
    "yawn": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "yawn_degree", "direction_of_face", "corners_of_the_mouth_cover", "yawn_false_action")
    },
    "distract": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "face_angle", "watch_point", "distraction_false_action")
    },
    "abnormal": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "intravital_human", "abnormal_type", "abnormal_false_action")
    },
    "ir": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "infrared_false_action")
    },
    "lens_block": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "cover_items", "cover_area", "camera_distance", "cover_false_action")
    },
    "seatbelt": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "safetybelt_color", "safetybelt_coverarea", "clothing_type", "clothes_color", "safetybelt_false_action")
    },
    "helmet": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "hat", "cap_color", "cap_cover")
    },
    "drink": {
        "part_attr": (
            "camera_pos", "glass_type", "is_hat", "mask", "agegroup", "gender", "time", "mix_light", "weather",
            "cup_orientation", "cup_type", "cover_area_face", "cup_cover", "hold_cup_hand", "cup_color")
    },
}

# 属性值的自定义排序映射
material_scene_sorted = {
    'smoke': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'cigarette_pose': ['smoking_jia_two_fingers', 'smoking_jia_multiple_fingers', 'smoking_pitch_front',
                           'smoking_pitch_back', 'smoking_no_hand'],
        'chirality': ['lefthand', 'righthand'],
        'type_of_cigarette': ['thick', 'thin'],
        'cigarettes_length': ['long', 'short'],
        'cigarette_light': ['yes', 'no'],
        'butt_towards': ['up', 'down', 'left', 'right', 'towards_to_len_only_a_point_in_area', ],
        'cigarette_surface_brightness': ['brightness_normal', 'brightness_overexposed'],
        'smoking_false_action': ['touch_mouth_or_face_with_hand', 'wired_headset', 'drink_milk_tea', 'eat_lollipop',
                                 'white_line_of_phone_side', 'nose_sucking_stick', 'pen', 'toothpick',
                                 'distance_greater_than_5cm',
                                 'light_effect'],
    },

    'call': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'calling': ['calling_ear_left', 'calling_ear_right', 'calling_mouth'],
        'chirality': ['lefthand', 'righthand'],
        'place_hold_phone': ['top', 'middle', 'bottom', 'below_the_bottom'],
        'phone_orientation': ['up', 'left', 'front', 'right', 'down'],
        'vehicle_vibrating': ['yes', 'no'],
        'phone_color': ['dark_color', 'light_color'],
        'calling_false_action': ['eating_something', 'drinking', 'touch_mouth_or_ear_with_hand', 'light_effect',
                                 'fringe'],
    },

    'eyeclose': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'eyes_status': ['all_close', 'one_open_one_close', 'narrow_eyes'],
        'eyes_makeup': ['colored_contacts_or_smoky_eyes', 'long_eyelashes', 'no'],
        'direction_of_face': ['face_to_forward', 'face_to_front_windshield_left', 'face_to_front_windshield_right',
                              'face_to_front_windshield_up', 'face_to_front_windshield_down', 'face_to_dashboard'],
        'close_eye_false_action': ['normal', 'open_small_eyes', 'one_open_one_close', 'blink_frequently', 'narrow_eyes',
                                   'look_down_to_left_main_controller', 'look_down_right_to_center_control',
                                   'long_eyelashes', 'down'],
        'close_eye_special_action': ['front_below_driver_windshield', 'dashboard'],
    },

    'yawn': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'yawn_degree': ['yawn_very_small', 'yawn_small', 'yawn_middle', 'yawn_big'],
        'direction_of_face': ['face_to_forward', 'face_to_a_pillar', 'face_to_interior_mirror', 'face_to_dashboard',
                              'face_to_center_console', 'left', 'right'],
        'corners_of_the_mouth_cover': ['both_corners_of_the_mouth_visible', 'one_corner_of_the_mouth_just_covered',
                                       'half_of_the_mouth_covered'],
        'yawn_false_action': ['surprised_expression', 'chewing_gum', 'singing', 'speaking', 'sternutation', 'smoking',
                              'whistle', 'laugh_out_loud', 'continuous_open_mouth'],
    },

    'distract': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'face_angle': ['left', 'right', 'rise', 'down'],
        'watch_point': ['m1', 'm2', 'm3'],
        'distraction_false_action': ['left_rear_view_mirror', 'right_rear_view_mirror', 'slight_head_tilt_up',
                                     'slight_angle_head_turn_left', 'slight_angle_head_turn_right',
                                     'slight_angle_head_tilt_down', 'normal'],
    },

    'abnormal': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'intravital_human': ['human', 'dummy'],
        'abnormal_type': ['unmanned', 'face_occlusion'],
        'abnormal_false_action': ['unocclusion', 'part_occlusion', 'mask', 'large_angle', 'cap',
                                  'female_large_angle_to_cover_face'],
    },

    'ir': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'infrared_false_action': ['myopia_glass', 'high_transmittanc_sunglasses', 'medium_transmittanc_sunglasses',
                                  'sun_glasses', 'others_object'],
    },

    'lens_block': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'cover_items': ['hand', 'palm', 'paper', 'fingers', 'clothes', 'towel', 'white_plastic_bag',
                        'black_plastic_bag',
                        'colored_plastic_bag', 'napkin', 'mask', 'hat', 'colored_tape', 'chutty', 'patternless_phone',
                        'patterned_phone', 'sun_visor', 'smudge'],
        'cover_area': ['percent85_to_percent90', 'greater_than_percent90', 'percent100'],
        'camera_distance': ['distance_0', 'distance_less_than_5', 'distance_greater_than_5'],
        'cover_false_action': ['shoot_the_roof_percent_0', 'shoot_the_roof_percent_20', 'shoot_the_roof_percent_40',
                               'shoot_the_roof_percent_60', 'shoot_the_roof_percent_80', 'shoot_the_roof_percent_90',
                               'shoot_the_roof_percent_100', 'shoot_the_left_window_percent_0',
                               'shoot_the_left_window_percent_20',
                               'shoot_the_left_window_percent_40', 'shoot_the_left_window_percent_60',
                               'shoot_the_left_window_percent_80',
                               'shoot_the_left_window_percent_100', 'shoot_the_right_window_percent_0',
                               'shoot_the_right_window_percent_20',
                               'shoot_the_right_window_percent_40', 'shoot_the_right_window_percent_60',
                               'shoot_the_right_window_percent_80',
                               'shoot_the_right_window_percent_100', 'shoot_the_front_window',
                               'transparent_plastic_bag',
                               'transparent_cup', 'night_darklight_haveperson', 'night_darklight_noperson',
                               'yin_yang_face',
                               'light_change', 'wave', 'shoot_the_roof', 'direct_light_lens'],
    },
    'seatbelt': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'safetybelt_color': ['dark_seatbelt', 'light_seatbelt'],
        'safetybelt_coverarea': ['less'],
        'clothing_type': ['winter_clothes', 'summer_clothes'],
        'clothes_color': ['dark_clothes', 'light_clothes', 'random_with_pattern'],
        'safetybelt_false_action': ['similar_color_with_seatbelt_and_clothes', 'touch_body', 'overexposure_light',
                                    'seatbelt_cover_than_50_percent'],
    },

    'helmet': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'hat': ['safe_helmet', 'peaked_cap', 'baseball_cap', 'knitted_hat', 'straw_hat', 'visor_cap',
                'clothes_with_hats',
                'army_cap', 'flat_top_hat', 'paper_box', 'bareheaded'],
        'cap_color': ['yellow', 'blue', 'red', 'white', 'others'],
        'cap_cover': ['half_forehead', 'all_forehead', 'no_forehead'],
    },

    'drink': {
        'camera_pos': ['a_pillar', 'central_control', 'a_pillar_right', 'rearview_mirror',
                       'bottom_center_of_the_windshield'],
        'glass_type': ['infrared_blocking_sunglasses', 'myopia_glass', 'sunglasses', 'noglass'],
        'is_hat': ['yes', 'no'],
        'mask': ['yes', 'no'],
        'agegroup': ['young_age', 'middle_age', 'old_age'],
        'gender': ['male', 'female'],
        'time': ['day', 'night'],
        'mix_light': ['normal_light', 'back_light', 'front_light', 'side_light',
                      'shade_alternately_twinkles_of_sunlight'],
        'weather': ['sunny', 'cloudy', 'small_rainy', 'heavy_rainy', 'snow'],

        'cup_orientation': ['above', 'under', 'left', 'right', 'other'],
        'cup_type': ['mineral_water_bottle', 'disposable_cup', 'vacuum_cup', 'ring_pull_can', 'other'],
        'cover_area_face': ['nonecover', 'littlecover', 'seriouscover'],
        'cup_cover': ['littlecover', 'middlecover', 'seriouscover'],
        'hold_cup_hand': ['left_hand', 'right_hand'],
        'cup_color': ['dark_color', 'light_color', 'white_color', 'other'],
    },
}


# 根据结果文件内容提取视频和报警类型映射
def extract_alarm_info_before(result_file):
    extract_alarm_info_before = {}
    with open(result_file, 'r',  encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行
            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}")
                raise e  # 直接抛出异常，中断统计
            alarmMask_str = data.get("alarmMask", "0")
            try:
                alarmMask = int(alarmMask_str)
            except ValueError:
                alarmMask = -1
            filepath = data.get("filepath")
            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_pro_name + ext
            if video_name not in extract_alarm_info_before:
                extract_alarm_info_before[video_name] = set()
            extract_alarm_info_before[video_name].add(alarmMask)
    print(f"总共提取了 {len(extract_alarm_info_before)} 个视频结果。")
    return extract_alarm_info_before


# 根据标定文件提取视频的标定信息
def extract_annotation_info(annotation_dir, material_scene_to_alarmMask, extract_alarm_info_before, test_mode):
    video_annotations = {}
    video_file_paths = {}  # 保存视频实际路径

    # 需要从标注文件中获取的键名，即属性值
    def handle_regular_annotation(video_name, first_annotation_data, material_scene, root, alarmMask):
        video_annotation = {
            # general
            "camera_pos": first_annotation_data.get("camera_pos"),
            "glass_type": first_annotation_data.get("glass_type"),
            "is_hat": first_annotation_data.get("is_hat"),
            "mask": first_annotation_data.get("mask"),
            "agegroup": first_annotation_data.get("agegroup"),
            "gender": first_annotation_data.get("gender"),
            "time": first_annotation_data.get("time"),
            "mix_light": first_annotation_data.get("mix_light"),
            "weather": first_annotation_data.get("weather"),

            # call_part_attr
            "calling": first_annotation_data.get("calling"),
            "chirality": first_annotation_data.get("chirality"),
            "place_hold_phone": first_annotation_data.get("place_hold_phone"),
            "phone_orientation": first_annotation_data.get("phone_orientation"),
            "vehicle_vibrating": first_annotation_data.get("vehicle_vibrating"),
            "phone_color": first_annotation_data.get("phone_color"),
            "calling_false_action": first_annotation_data.get("calling_false_action"),

            # smoke_part_attr
            "cigarette_pose": first_annotation_data.get("cigarette_pose"),
            "type_of_cigarette": first_annotation_data.get("type_of_cigarette"),
            "cigarettes_length": first_annotation_data.get("cigarettes_length"),
            "cigarette_light": first_annotation_data.get("cigarette_light"),
            "butt_towards": first_annotation_data.get("butt_towards"),
            "cigarette_surface_brightness": first_annotation_data.get("cigarette_surface_brightness"),
            "smoking_false_action": first_annotation_data.get("smoking_false_action"),

            # eyeclose_part_attr
            "eyes_status": first_annotation_data.get("eyes_status"),
            "eyes_makeup": first_annotation_data.get("eyes_makeup"),
            "direction_of_face": first_annotation_data.get("direction_of_face"),
            "close_eye_false_action": first_annotation_data.get("close_eye_false_action"),
            "close_eye_special_action": first_annotation_data.get("close_eye_special_action"),

            # yawn_part_attr
            "yawn_degree": first_annotation_data.get("yawn_degree"),
            "direction_of_face": first_annotation_data.get("direction_of_face"),
            "corners_of_the_mouth_cover": first_annotation_data.get("corners_of_the_mouth_cover"),
            "yawn_false_action": first_annotation_data.get("yawn_false_action"),

            # ir_part_attr
            "infrared_false_action": first_annotation_data.get("infrared_false_action"),

            # distract_part_attr
            "face_angle": first_annotation_data.get("face_angle"),
            "watch_point": first_annotation_data.get("watch_point"),
            "distraction_false_action": first_annotation_data.get("distraction_false_action"),

            # seatbelt_part_attr
            "safetybelt_color": first_annotation_data.get("safetybelt_color"),
            "safetybelt_coverarea": first_annotation_data.get("safetybelt_coverarea"),
            "clothing_type": first_annotation_data.get("clothing_type"),
            "clothes_color": first_annotation_data.get("clothes_color"),
            "safetybelt_false_action": first_annotation_data.get("safetybelt_false_action"),

            # abnormal_part_attr
            "intravital_human": first_annotation_data.get("intravital_human"),
            "abnormal_type": first_annotation_data.get("abnormal_type"),
            "abnormal_false_action": first_annotation_data.get("abnormal_false_action"),

            # lenBlock_part_attr
            "cover_items": first_annotation_data.get("cover_items"),
            "cover_area": first_annotation_data.get("cover_area"),
            "camera_distance": first_annotation_data.get("camera_distance"),
            "cover_false_action": first_annotation_data.get("cover_false_action"),

            # helmet_part_attr
            "hat": first_annotation_data.get("hat"),
            "cap_color": first_annotation_data.get("cap_color"),
            "cap_cover": first_annotation_data.get("cap_cover"),

            # drink_part_attr
            "cup_orientation": first_annotation_data.get("cup_orientation"),
            "cup_type": first_annotation_data.get("cup_type"),
            "cover_area_face": first_annotation_data.get("cover_area_face"),
            "cup_cover": first_annotation_data.get("cup_cover"),
            "hold_cup_hand": first_annotation_data.get("hold_cup_hand"),
            "cup_color": first_annotation_data.get("cup_color"),

            "sample_np_type": first_annotation_data.get("sample_np_type"),
            "material_scene": first_annotation_data.get("material_scene"),
            "alarmMask": alarmMask
        }
        video_annotations[video_name] = video_annotation
        video_file_paths[video_name] = os.path.join(root, video_name)

    # 遍历标定文件夹
    for root, dirs, files in os.walk(annotation_dir):
        for filename in files:
            #  不在统计结果中的素材和非标注文件不做处理
            if not filename.endswith('.txt') and filename not in extract_alarm_info_before:
                continue

            if filename in extract_alarm_info_before:
                video_pro_name, ext = os.path.splitext(os.path.basename(filename))
                video_alarm_txt_name = video_pro_name + '.txt'
                txt_file_path = os.path.join(root, video_alarm_txt_name)

                try:
                    with open(txt_file_path, 'r', errors='ignore') as file:
                        lines = file.readlines()
                        if not lines:
                            continue

                        first_annotation_data = json.loads(lines[0])

                        material_scene = first_annotation_data.get("material_scene")
                        alarmMask = material_scene_to_alarmMask.get(material_scene, 0)

                        handle_regular_annotation(filename, first_annotation_data, material_scene, root, alarmMask)
                except FileNotFoundError:
                    print(f"标注文件不存在: {txt_file_path}")
                    raise
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON in file: {txt_file_path} - Error: {e}")
                    raise
    print(f"提取 {len(video_annotations)} 个视频标定结果。")
    return video_annotations, video_file_paths


def extract_alarm_info(result_file, video_annotations, test_mode):
    video_alarm_map = {}

    with open(result_file, 'r',  encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()  # 去除首尾空白字符
            if not line:
                continue  # 跳过空行
            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}, Error: {e}")
                raise
            alarmMask_str = data.get("alarmMask", "0")
            try:
                alarmMask = int(alarmMask_str)
            except ValueError:
                alarmMask = -1

            filepath = data.get("filepath")
            video_pro_name, ext = os.path.splitext(os.path.basename(filepath))

            video_name = video_pro_name + ext
            if video_name in video_annotations:
                video_alarm_map.setdefault(video_name, set()).add(alarmMask)
    return video_alarm_map


def update_filepath(video_name_old, filepath, video_paths):
    for video_src_name, src_path in video_paths.items():
        if video_name_old == video_src_name:
            return os.path.normpath(src_path)  # 返回新的文件路径，用于去除多余斜杠
    return filepath

def handle_alarm_mask_match(data, video_name, video_annotations, test_mode, alarmMask):

    if video_name in video_annotations:
        annotation = video_annotations[video_name]
        expected_alarmMask = annotation.get("alarmMask", 0)
        material_scene = annotation.get("material_scene", "unknown")
        sample_np_type = annotation.get("sample_np_type", "unknown")

        # 检查 alarmMask 是否匹配
        if data.get("alarmMask") == expected_alarmMask :
            filename = f"DMS_{expected_alarmMask}_{material_scene}_{sample_np_type}.txt"
            # print(data)
            return filename, data  # 返回文件名和数据
        else:
            # 如果不匹配，返回空结果
            empty_result = {
                "filepath": data.get("filepath"),
                "alarmTime": []
            }
            filename = f"DMS_{expected_alarmMask}_{material_scene}_{sample_np_type}.txt"
            return filename, empty_result
    return None, None

def split_and_merge_result_file(result_file, video_annotations, output_dir, video_paths, test_mode):
    # 确保目标目录存在
    os.makedirs(output_dir, exist_ok=True)

    detailed_files = defaultdict(lambda: defaultdict(list))

    with open(result_file, 'r', encoding='utf-8', errors='ignore') as file:
        for line in file:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"Invalid JSON format: {line}")
                raise e  # 直接抛出异常，中断统计

            alarmMask = int(data.get("alarmMask", 0))

            filepath = data.get("filepath")
            video_name_old, ext = os.path.splitext(os.path.basename(filepath))
            video_name = video_name_old + ext

            # 更新文件路径
            data['filepath'] = update_filepath(video_name_old, filepath, video_paths)
            for video_src_name, src_path in video_paths.items():
                    data['filepath'] = os.path.normpath(src_path)  # 修改文件路径，用于拆分结果

            data["alarmMask"] = alarmMask
            filename, merged_data = handle_alarm_mask_match(data, video_name, video_annotations, test_mode, alarmMask)
            if filename:
                detailed_files[filename][video_name].append(merged_data)

    # 写入合并后的结果到拆分的文件中
    if detailed_files:
        print("{:^20} {:>30}".format("拆分结果", "路径"))
    for filename, videos_data in detailed_files.items():
        output_file = os.path.join(output_dir, filename)
        print(f"{filename:^10}   {output_file:>10}")

        with open(output_file, 'w') as file:
            for video_name, entries in videos_data.items():
                # 合并同一视频的结果
                merged_result = {"filepath": next(entry["filepath"] for entry in entries)}
                alarm_times = [entry.get("alarmTime") for entry in entries if "alarmTime" in entry]
                merged_result["alarmTime"] = sorted(set(alarm_time for sublist in alarm_times for alarm_time in
                                                        (sublist if isinstance(sublist, list) else [sublist])))

                file.write(json.dumps(merged_result, ensure_ascii=False) + "\n")

    print("结果文件拆分并合并完毕。")


# 拷贝视频到指定目录
def copy_videos(videos, src_path, error_dir):
    src_dir = os.path.dirname(src_path)
    if not os.path.exists(error_dir):
        os.makedirs(error_dir)
    for video_name in videos:
        dst_path = os.path.join(error_dir, video_name)
        if os.path.exists(src_dir):
            shutil.copy2(src_path, error_dir)
            print(f"copy {src_path} to {dst_path}")
        else:
            print(f"Source file does not exist: {src_path}")


def process_videos(result_file, annotation_dir, dst_dir, output_dir_path, test_mode, print_FN_FP, split_result,
                   copy_video):

    # 初始化映射关系和基础数据
    material_scene_to_alarmMask = {v: k for k, v in alarmMask_to_material_scene.items()}

    # 数据提取阶段
    video_alarm_map_before = extract_alarm_info_before(result_file)
    video_annotations, video_paths = extract_annotation_info(annotation_dir, material_scene_to_alarmMask,
                                                             video_alarm_map_before, test_mode)
    video_alarm_map = extract_alarm_info(result_file, video_annotations, test_mode)

    # 文件处理阶段
    print(f"*****未校对视频数：{len(video_alarm_map) - len(video_annotations)}")
    print("*******************************************************************************************************")

    if split_result:
        split_and_merge_result_file(result_file, video_annotations, output_dir_path, video_paths, test_mode)

    # 初始化统计数据结构
    stats = initialize_statistics(material_scene_sorted)

    # 核心统计逻辑
    process_statistics(video_alarm_map, video_annotations, stats)

    # 视频拷贝处理
    if copy_video:
        handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir)

    # 结果输出
    print_statistics(stats, print_FN_FP)

    return stats

def initialize_statistics(material_scene_sorted):
    """初始化统计数据结构"""
    return {
        'positive': defaultdict(lambda: defaultdict(int)),
        'negative': defaultdict(lambda: defaultdict(int)),
        'positive_correct': defaultdict(int),
        'negative_correct': defaultdict(int),
        'attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_attribute_positive': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_attribute_negative': defaultdict(lambda: defaultdict(lambda: defaultdict(int))),
        'total_positive': defaultdict(int),
        'total_negative': defaultdict(int),
        'material_scene_sorted': material_scene_sorted
    }


def process_statistics(video_alarm_map, video_annotations, stats):
    """处理核心统计逻辑"""
    for video_name, alarms in video_alarm_map.items():
        if video_name not in video_annotations:
            print(f"{video_name} 存在结果文件，缺失标注文件")
            continue

        annotation = video_annotations[video_name]
        material_scene = annotation.get("material_scene")
        sample_np_type = annotation.get("sample_np_type")
        alarmMask = annotation.get("alarmMask")

        # 基础样本统计
        update_base_stats(stats, material_scene, sample_np_type, alarms, alarmMask)

        # 属性级统计
        if material_scene in attributes:
            update_attribute_stats(stats, material_scene, sample_np_type, annotation, alarms, alarmMask)


def update_base_stats(stats, material_scene, sample_np_type, alarms, alarmMask):
    """更新基础样本统计"""
    if sample_np_type == "positive":
        stats['total_positive'][material_scene] += 1
        if alarmMask in alarms:
            stats['positive_correct'][material_scene] += 1
        else:
            stats['positive'][material_scene]['total'] += 1
    elif sample_np_type == "negative":
        stats['total_negative'][material_scene] += 1
        if alarmMask not in alarms:
            stats['negative_correct'][material_scene] += 1
        else:
            stats['negative'][material_scene]['total'] += 1


def update_attribute_stats(stats, material_scene, sample_np_type, annotation, alarms, alarmMask):
    """更新属性级统计"""
    for attr in attributes[material_scene]["part_attr"]:
        value = annotation.get(attr)
        if not value: continue

        value_str = ','.join(map(str, value)) if isinstance(value, list) else value
        key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

        # 更新总数
        stats[f'total_{key.split("_")[1]}'][value_str] += 1

        # 更新各属性值总数
        if (sample_np_type == "positive" ) or  (sample_np_type == "negative" ):
            stats[f'total_{key}'][material_scene][attr][value_str] += 1

        # 更新错误计数
        if (sample_np_type == "positive" and alarmMask not in alarms) or \
                (sample_np_type == "negative" and alarmMask in alarms):
            stats[key][material_scene][attr][value_str] += 1


# def handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir):
#     """处理视频拷贝逻辑（仅显示进度条）"""
#
#     def copy_files(sample_np_type, error_type):
#         # 先收集需要处理的文件
#         process_files = []
#         for video_name, annotation in video_annotations.items():
#             if annotation.get("sample_np_type") == sample_np_type:
#                 if video_name in video_alarm_map:
#                     alarms = video_alarm_map[video_name]
#                     alarmMask = annotation.get("alarmMask")
#                     condition = (alarmMask not in alarms) if sample_np_type == "positive" else (alarmMask in alarms)
#                     if condition:
#                         process_files.append(video_name)
#
#         # 创建进度条
#         with tqdm(total=len(process_files),
#                  desc=f"{'漏检视频' if sample_np_type == 'positive' else '误检视频'}",
#                  unit="file") as pbar:
#             for video_name in process_files:
#                 if src_path := video_paths.get(video_name):
#                     alarmMask = video_annotations[video_name].get("alarmMask")
#                     error_dir = os.path.join(
#                         dst_dir,
#                         error_type,
#                         f"DMS_{alarmMask_to_material_scene.get(alarmMask, 'unknown')}"
#                     )
#                     os.makedirs(error_dir, exist_ok=True)
#                     shutil.copy2(src_path, error_dir)
#                 pbar.update(1)  # 无论成功失败都更新进度
#
#     print("\n\033[32m=== 开始视频拷贝 ===\033[0m")
#     copy_files("positive", "漏检视频")
#     copy_files("negative", "误检视频")
#     print("\033[32m=== 拷贝完成 ===\033[0m\n")

def handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir):
    """处理视频拷贝逻辑（仅显示进度条）"""

    def copy_files(sample_np_type, error_type):
        # 先收集需要处理的文件
        process_files = []
        for video_name, annotation in video_annotations.items():
            if annotation.get("sample_np_type") == sample_np_type:
                if video_name in video_alarm_map:
                    alarms = video_alarm_map[video_name]
                    alarmMask = annotation.get("alarmMask")
                    condition = (alarmMask not in alarms) if sample_np_type == "positive" else (alarmMask in alarms)
                    if condition:
                        process_files.append(video_name)

        # 创建进度条
        with tqdm(total=len(process_files),
                 desc=f"{'漏检视频' if sample_np_type == 'positive' else '误检视频'}",
                 unit="file") as pbar:
            for video_name in process_files:
                if src_path := video_paths.get(video_name):
                    alarmMask = video_annotations[video_name].get("alarmMask")
                    error_dir = os.path.join(
                        dst_dir,
                        error_type,
                        f"DMS_{alarmMask_to_material_scene.get(alarmMask, 'unknown')}"
                    )
                    os.makedirs(error_dir, exist_ok=True)

                    # 拷贝视频文件
                    shutil.copy2(src_path, error_dir)
                    print(f"copy {src_path} to {os.path.join(error_dir, os.path.basename(src_path))}")

                    # 拷贝标签文件
                    video_pro_name, ext = os.path.splitext(os.path.basename(video_name))
                    txt_file_name = video_pro_name + '.txt'
                    txt_src_path = os.path.join(os.path.dirname(src_path), txt_file_name)
                    if os.path.exists(txt_src_path):
                        shutil.copy2(txt_src_path, error_dir)
                        print(f"copy {txt_src_path} to {os.path.join(error_dir, txt_file_name)}")
                    else:
                        print(f"标签文件 {txt_src_path} 不存在，无法拷贝。")

                pbar.update(1)  # 无论成功失败都更新进度

    print("\n\033[32m=== 开始视频拷贝 ===\033[0m")
    copy_files("positive", "漏检视频")
    copy_files("negative", "误检视频")
    print("\033[32m=== 拷贝完成 ===\033[0m\n")

# def handle_video_copy(video_annotations, video_alarm_map, video_paths, dst_dir):
#     """处理视频拷贝逻辑（仅显示进度条）"""
#
#     def copy_files(sample_np_type, error_type):
#         # 先收集需要处理的文件
#         process_files = []
#         for video_name, annotation in video_annotations.items():
#             if annotation.get("sample_np_type") == sample_np_type:
#                 if video_name in video_alarm_map:
#                     alarms = video_alarm_map[video_name]
#                     alarmMask = annotation.get("alarmMask")
#                     condition = (alarmMask not in alarms) if sample_np_type == "positive" else (alarmMask in alarms)
#                     if condition:
#                         process_files.append((video_name, annotation))
#
#         # 创建进度条
#         with tqdm(total=len(process_files),
#                  desc=f"{'漏检视频' if sample_np_type == 'positive' else '误检视频'}",
#                  unit="file") as pbar:
#             for video_name, annotation in process_files:
#                 if src_path := video_paths.get(video_name):
#                     alarmMask = video_annotations[video_name].get("alarmMask")
#                     material_scene = annotation.get("material_scene", "unknown")
#                     watch_point = annotation.get("watch_point", "unknown")
#
#                     if material_scene == "distract" and watch_point in ['m1', 'm2', 'm3']:
#                         # 根据 watch_point 创建不同的文件夹
#                         error_dir = os.path.join(
#                             dst_dir,
#                             error_type,
#                             f"DMS_{alarmMask_to_material_scene.get(alarmMask, 'unknown')}",
#                             watch_point
#                         )
#                     else:
#                         error_dir = os.path.join(
#                             dst_dir,
#                             error_type,
#                             f"DMS_{alarmMask_to_material_scene.get(alarmMask, 'unknown')}"
#                         )
#
#                     os.makedirs(error_dir, exist_ok=True)
#
#                     # 拷贝视频文件
#                     shutil.copy2(src_path, error_dir)
#                     print(f"copy {src_path} to {os.path.join(error_dir, os.path.basename(src_path))}")
#
#                     # 拷贝标签文件
#                     video_pro_name, ext = os.path.splitext(os.path.basename(video_name))
#                     txt_file_name = video_pro_name + '.txt'
#                     txt_src_path = os.path.join(os.path.dirname(src_path), txt_file_name)
#                     if os.path.exists(txt_src_path):
#                         shutil.copy2(txt_src_path, error_dir)
#                         print(f"copy {txt_src_path} to {os.path.join(error_dir, txt_file_name)}")
#                     else:
#                         print(f"标签文件 {txt_src_path} 不存在，无法拷贝。")
#
#                 pbar.update(1)  # 无论成功失败都更新进度
#
#     print("\n\033[32m=== 开始视频拷贝 ===\033[0m")
#     copy_files("positive", "漏检视频")
#     copy_files("negative", "误检视频")
#     print("\033[32m=== 拷贝完成 ===\033[0m\n")



def print_statistics(stats, print_details):
    """打印总数统计"""
    print(
        "\n\033[32m***************************************************************每种算法的正样本总数和负样本总数************************************************************\033[0m")
    """打印统计结果"""
    # 打印基础统计
    print_header = ("算法", "正样本总数", "正样本漏检数", "正样本正检数", "召回率",
                    "负样本总数", "误检总数", "精准率")
    print("{:<40} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10} {:<10}".format(*print_header))



    for material_scene in stats['material_scene_sorted']:
        tp = stats['total_positive'][material_scene]   # 正样本总数
        fp = stats['negative'][material_scene]['total']  # 误检数
        tn = stats['total_negative'][material_scene]  # 负样本总数
        fn = stats['positive'][material_scene]['total']  # 漏检数
        correct_p = stats['positive_correct'][material_scene]  # 正样本正检数
        correct_n = stats['negative_correct'][material_scene]  # 负样本正检数

        recall = safe_division(correct_p, tp) * 100
        precision = safe_division(correct_p, (correct_p + fp)) * 100
        if (tp or tn ) > 0:
            print(f"{material_scene:<40}{tp:>8}\t{fn:>10}\t"
                  f"{correct_p:>15}\t{recall:>12.2f}%\t"
                  f"{tn:>10}\t{fp:>10}\t{precision:>12.2f}%")

    # 打印详细属性统计
    if print_details:
        print_attribute_stats(stats, "positive", "漏检")
        print_attribute_stats(stats, "negative", "误检")


def print_attribute_stats(stats, sample_np_type, error_type):
    """打印属性级统计"""
    print(
        "\n\033[32m************************************************每种属性值的{:^5}数************************************************\033[0m".format(
            error_type))

    # 确定指标类型和计算方式
    rate_name = "召回率" if sample_np_type == "positive" else "误检率"
    sample_np_type_name = "正样本" if sample_np_type == "positive" else "负样本"
    key = 'attribute_positive' if sample_np_type == "positive" else 'attribute_negative'

    # 强制按 material_scene_sorted 的顺序遍历场景
    for material_scene in material_scene_sorted:
        # 跳过没有统计数据的场景
        if material_scene not in stats[key]:
            continue

        print(f"\033[36m{'*' * 20} {material_scene.center(20)} {'*' * 20}\033[0m")
        print("{:^25} {:<35} {:>20} {:>10} {:>10} {:>10}".format(
            "属性", "属性值", f"{sample_np_type_name}总数", f"{error_type}数", "正检数",  rate_name))

        # 按预定义的属性顺序遍历
        for attr in material_scene_sorted[material_scene]:
            # 跳过没有数据的属性
            if attr not in stats[key][material_scene]:
                continue

            print(f"{attr:^20}")
            for value in material_scene_sorted[material_scene][attr]:
                errors = stats[key][material_scene][attr].get(value, 0)
                total = stats[f'total_{key}'][material_scene][attr].get(value, 0)
                correct = total - errors

                # 动态计算比率
                if sample_np_type == "positive":
                    rate = safe_division(correct, total) * 100  # 召回率 = 正确数/正样本总数
                else:
                    rate = safe_division(errors, total) * 100  # 误检率 = 误检数/负样本总数

                print(f"{'':^35} {value:<35} "
                      f"{total:>14} "
                      f"{errors:>12} "
                      f"{correct if sample_np_type == 'positive' else total - errors:>13} "
                      f"{rate:>13.2f}%")


def safe_division(numerator, denominator):
    """安全除法"""
    return numerator / denominator if denominator else -1

def write_results_to_excel(output_dir, material_scene_sorted, stats):
    from openpyxl.styles import Font, Alignment, PatternFill

    def apply_style(sheet):
        """统一应用样式"""
        header_fill = PatternFill(start_color="3CB371", end_color="3CB371", fill_type="solid")
        for row in sheet.iter_rows():
            for cell in row:
                cell.font = Font(name="Microsoft YaHei")
                cell.alignment = Alignment(horizontal="center", vertical="center")

        # 第一行样式（表头）
        for cell in sheet[1]:
            cell.fill = header_fill
            cell.font = Font(bold=True, name="Microsoft YaHei")

        # 第二列左对齐
        for row in sheet.iter_rows(min_row=2, min_col=2, max_col=2):
            for cell in row: cell.alignment = Alignment(horizontal="left")

        # 合并“汇总”和其后一列的单元格
        sheet.merge_cells(start_row=2, start_column=1, end_row=2, end_column=2)

    def add_attribute_rows(sheet, scene, attrs):
        """填充属性数据"""
        start_row = 3
        current_attr = None

        for attr in material_scene_sorted[scene]:
            for value in material_scene_sorted[scene][attr]:
                # 获取统计数据
                pos_total = stats['total_attribute_positive'][scene].get(attr, {}).get(value, 0)  # 正样本子属性总数
                pos_error = stats['attribute_positive'][scene].get(attr, {}).get(value, 0)  # 正样本子属性漏检数量
                neg_total = stats['total_attribute_negative'][scene].get(attr, {}).get(value, 0)
                neg_error = stats['attribute_negative'][scene].get(attr, {}).get(value, 0)

                # 计算结果
                correct = pos_total - pos_error  # 正样本正检数

                # 添加行
                row = [
                    attr, value, pos_total, pos_error, correct,
                    f"{correct / pos_total * 100:.2f}%" if pos_total else "#DIV/0!",  # 召回率
                    neg_total, neg_error, neg_total - neg_error,
                    f"{neg_error / neg_total * 100:.2f}%" if neg_total else "#DIV/0!",  # 误检率
                    f"{(correct / (correct + neg_error)) * 100:.2f}%" if (correct + neg_error) else "#DIV/0!"
                ]
                sheet.append(row)

                # 合并属性列
                if attr != current_attr:
                    if current_attr:
                        sheet.merge_cells(f"A{start_row}:A{sheet.max_row - 1}")
                        # 在合并区域的第一行添加“上框线”
                        for cell in sheet[start_row]:
                            cell.border = Border(top=Side(style='thin'))
                        # 在合并区域的最后一行添加“下框线”
                        for cell in sheet[sheet.max_row - 1]:
                            cell.border = Border(bottom=Side(style='thin'))
                    current_attr = attr
                    start_row = sheet.max_row

        # 手动合并最后一个属性列
        if current_attr:
            sheet.merge_cells(f"A{start_row}:A{sheet.max_row}")
            # 在合并区域的第一行添加“上框线”
            for cell in sheet[start_row]:
                cell.border = Border(top=Side(style='thin'))
            # 在合并区域的最后一行添加“下框线”
            for cell in sheet[sheet.max_row]:
                cell.border = Border(bottom=Side(style='thin'))

    def auto_adjust_column_width(sheet):
        """自适应调整"""
        for col in sheet.columns:
            max_length = 0
            column = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    # 计算单元格内容的长度
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
                except:
                    pass
            # 设置列宽为最大宽度 + 2（留出一些额外空间）
            adjusted_width = (max_length + 8)
            sheet.column_dimensions[column].width = adjusted_width

            sheet.row_dimensions[1].height = 30  # 设置第一行的行高
            sheet.row_dimensions[2].height = 30  # 设置第二行的行高


    workbook = openpyxl.Workbook()

    for scene in material_scene_sorted:
        pos_total = stats['total_positive'].get(scene, 0)
        neg_total = stats['total_negative'].get(scene, 0)
        if not (pos_total or neg_total): continue

        sheet = workbook.create_sheet(scene)
        sheet.append(["属性", "属性值", "正样本总数", "正样本漏检数", "正样本正检数", "召回率",
                      "负样本总数", "负样本误检数", "负样本正检数", "误检率", "精确率"])

        # 添加汇总行
        pos_error = sum(stats['positive'][scene].values())  # 正检总数
        neg_error = sum(stats['negative'][scene].values())  # 误检总数


        sheet.append([
            "汇总", "", pos_total, pos_error, pos_total - pos_error,
            f"{(pos_total - pos_error) / pos_total * 100:.2f}%" if pos_total else "#DIV/0!",  # 召回率
            neg_total, neg_error, neg_total - neg_error,
            f"{neg_error / neg_total * 100:.2f}%" if neg_total else "#DIV/0!",  # 误检率
            f"{(pos_total - pos_error) / ((pos_total - pos_error) + neg_error) * 100:.2f}%" if ((pos_total - pos_error) + neg_error)  else "#DIV/0!"
        ])

        # 填充详细数据
        add_attribute_rows(sheet, scene, material_scene_sorted[scene])
        apply_style(sheet)
        sheet.freeze_panes = "A2"  # 冻结首行

       # 自适应调整列宽
        auto_adjust_column_width(sheet)

    if 'Sheet' in workbook: del workbook['Sheet']
    workbook.save(os.path.join(output_dir, "analysis_result.xlsx"))
    print(
        f"\n\033[32m-----------EXCEL统计表格已保存到{output_dir}\\analysis_result.xlsx\033[0m")


if __name__ == "__main__":

    source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\DMS"
    # source_video_and_annotation_dir_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\23092_曹操出行双摄项目二期\素材采集\已标注素材"

    test_mode = "DMS"

    print_FN_FP = 1   # 打印漏误检详细分布
    split_result = 0  # 拆分结果文件
    copy_video = 0    # 拷贝漏误检视频
    write_excel = 0   # 生成统计结果表格


    # result_file_path = r"E:\android_projects\24121兆岳MT8666\D1-1[24121-2]\测试结果\源结果\DMS_result_2.1.026.1403.105_20230324_234851.txt"
    ##对应的测试DMS_result.txt路径
    result_file_path = r"G:\24121兆岳MT8666\D1-1[24121-2]\测试结果\源结果\DMS_result_2.1.026.1403.105_20230324_234851.txt"
    # output_dir_path = r"E:\android_projects\24121兆岳MT8666\D1-1[24121-2]\测试结果"  # 拆分结果储存路径
    output_dir_path = r"G:\24121兆岳MT8666\D1-1[24121-2]\测试结果"  # 拆分结果储存路径
    # destination_dir_path = r"E:\android_projects\24121兆岳MT8666\D1-1[24121-2]"  # 漏误检视频储存路径
    destination_dir_path = r"G:\24121兆岳MT8666\D1-1[24121-2]"  # 漏误检视频储存路径


    stats = process_videos(result_file_path, source_video_and_annotation_dir_path, destination_dir_path, output_dir_path,
                   test_mode, print_FN_FP, split_result, copy_video)
    if write_excel:
        write_results_to_excel(output_dir_path, material_scene_sorted, stats)