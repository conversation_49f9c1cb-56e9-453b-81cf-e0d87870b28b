# BSD效果测试工具UI版本 v4.0

## 功能概述

这是一个带有图形用户界面的BSD（盲点检测）效果测试工具，用于分析测试结果、提取标定信息、统计准确率等。**此版本完全按照原始脚本逻辑重新构建，确保与原脚本功能100%一致。**

## 版本更新说明

### v4.0 更新内容
- **完全重构**：按照原始`BSD_effect_test_4.0.py`脚本的精确逻辑重新实现
- **统计算法修复**：修复了统计逻辑，确保与原脚本输出完全一致
- **属性分析增强**：完整实现了原脚本的复杂属性统计功能
- **算法匹配逻辑**：精确复制了原脚本中的算法匹配条件判断
- **输出格式统一**：日志输出格式与原脚本保持一致

## 主要功能

### 1. 路径配置
- **源视频和标定目录**: 选择包含视频文件和对应标定文件的目录
- **结果文件**: 选择测试结果文件（.txt格式）
- **输出目录**: 设置拆分结果文件的输出目录
- **目标目录**: 设置漏误检视频的存储目录

### 2. 功能配置
- **测试模式**: 当前支持BSD模式
- **打印漏误检详细分布**: 是否显示详细的漏检和误检分析
- **拆分结果文件**: 是否将结果按算法和样本类型拆分
- **拷贝漏误检视频**: 是否将检测错误的视频拷贝到指定目录

### 3. 高级配置
- **编辑算法映射**: 配置报警类型到算法名称的映射关系
- **编辑属性定义**: 配置各算法的属性定义
- **编辑排序规则**: 配置属性值的排序规则

### 4. 配置管理
- **保存配置**: 将当前设置保存到配置文件
- **加载配置**: 从配置文件加载之前保存的设置
- **重置配置**: 重置所有设置到默认值

## 使用方法

### 1. 启动程序
```bash
python BSD_effect_test_UI.py
```

### 2. 配置参数
1. 点击"浏览"按钮选择相应的目录和文件
2. 根据需要调整功能开关
3. 如需修改算法配置，点击相应的"编辑"按钮

### 3. 开始处理
1. 确认所有必要参数已配置
2. 点击"开始处理"按钮
3. 在日志区域查看处理进度和结果

### 4. 查看结果
- 统计信息会显示在日志区域
- 如果启用了拆分功能，结果文件会保存到输出目录
- 如果启用了视频拷贝功能，错误视频会分类保存到目标目录

## 输出说明

### 统计信息
程序会输出每种算法的：
- 正样本总数、漏检数、正检数、召回率
- 负样本总数、误检数、精确率

### 拆分文件
如果启用拆分功能，会按以下格式生成文件：
- `BSD_{算法名}_{样本类型}.txt`

### 视频分类
如果启用视频拷贝功能，会创建以下目录结构：
```
目标目录/
├── 漏检视频/
│   └── BSD_{算法名}/
└── 误检视频/
    └── BSD_{算法名}/
```

## 配置文件

程序会自动创建 `bsd_config.ini` 配置文件来保存设置，包括：
- 路径配置
- 功能开关设置

## 注意事项

1. 确保源目录中包含视频文件和对应的标定文件（.txt格式）
2. 结果文件应为JSON Lines格式
3. 标定文件的第一行应包含完整的标定信息
4. 程序运行时会在日志区域显示详细的处理信息
5. 大文件处理可能需要较长时间，请耐心等待

## 技术特性

- 基于tkinter的图形用户界面
- 多线程处理，避免界面冻结
- 实时日志显示
- 配置文件自动保存/加载
- 灵活的参数配置
- 完整的错误处理和验证

## 系统要求

- Python 3.6+
- tkinter（通常随Python安装）
- 标准库：json, os, shutil, threading, configparser, collections
