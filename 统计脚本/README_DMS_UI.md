# DMS效果测试工具UI版本

## 功能概述

这是一个带有图形用户界面的DMS（驾驶员监控系统）效果测试工具，用于分析测试结果、提取标定信息、统计准确率等。

## 主要功能

### 1. 路径配置
- **源视频和标定目录**: 选择包含视频文件和对应标定文件的目录
- **结果文件**: 选择测试结果文件（.txt格式）
- **输出目录**: 设置拆分结果文件的输出目录
- **目标目录**: 设置漏误检视频的存储目录

### 2. 功能配置
- **测试模式**: 当前支持DMS模式
- **打印漏误检详细分布**: 是否显示详细的漏检和误检分析
- **拆分结果文件**: 是否将结果按场景和样本类型拆分
- **拷贝漏误检视频**: 是否将检测错误的视频拷贝到指定目录
- **导出Excel报告**: 是否生成Excel格式的详细报告

### 3. 高级配置
- **编辑报警映射**: 配置报警掩码到场景名称的映射关系
- **编辑属性定义**: 配置各场景的属性定义
- **编辑排序规则**: 配置属性值的排序规则
- **查看场景说明**: 查看DMS各场景的详细说明

### 4. 配置管理
- **保存配置**: 将当前设置保存到配置文件
- **加载配置**: 从配置文件加载之前保存的设置
- **重置配置**: 重置所有设置到默认值

## DMS场景说明

### 支持的检测场景

1. **call (打电话)** - 报警掩码: 1
   - 检测驾驶员是否在打电话，包括手持电话、免提通话等行为

2. **smoke (抽烟)** - 报警掩码: 2
   - 检测驾驶员是否在抽烟，包括点烟、吸烟等行为

3. **eyeclose (闭眼)** - 报警掩码: 4
   - 检测驾驶员是否闭眼或眯眼，用于疲劳驾驶检测

4. **yawn (打哈欠)** - 报警掩码: 8
   - 检测驾驶员打哈欠行为，疲劳驾驶的重要指标

5. **distract (分心)** - 报警掩码: 16
   - 检测驾驶员注意力分散，如看手机、看副驾驶等

6. **abnormal (异常)** - 报警掩码: 32
   - 检测异常情况，如无人驾驶、面部遮挡等

7. **ir (红外)** - 报警掩码: 64
   - 红外相关检测，如红外阻挡等

8. **lens_block (镜头遮挡)** - 报警掩码: 128
   - 检测摄像头镜头是否被遮挡

9. **seatbelt (安全带)** - 报警掩码: 256
   - 检测驾驶员是否系安全带

10. **helmet (头盔)** - 报警掩码: 1024
    - 检测驾驶员是否佩戴头盔（适用于摩托车等）

11. **drink (喝水)** - 报警掩码: 8192
    - 检测驾驶员喝水或饮用其他液体的行为

### 属性维度

每个场景都包含多个属性维度：

#### 基础属性（所有场景共有）
- **camera_pos**: 摄像头位置
- **glass_type**: 眼镜类型
- **is_hat**: 是否戴帽子
- **mask**: 是否戴口罩
- **agegroup**: 年龄组
- **gender**: 性别
- **time**: 时间（白天/夜间）
- **mix_light**: 光照条件
- **weather**: 天气条件

#### 场景特定属性
每个场景根据其特点有不同的专属属性，如：
- **打电话场景**: 通话方式、手持方向、手机颜色等
- **抽烟场景**: 香烟姿势、香烟类型、点燃状态等
- **闭眼场景**: 眼部状态、眼妆、面部方向等

## 使用方法

### 1. 启动程序
```bash
python DMS_effect_test_UI.py
```

### 2. 配置参数
1. 点击"浏览"按钮选择相应的目录和文件
2. 根据需要调整功能开关
3. 如需修改场景配置，点击相应的"编辑"按钮

### 3. 开始处理
1. 确认所有必要参数已配置
2. 点击"开始处理"按钮
3. 在日志区域查看处理进度和结果

### 4. 查看结果
- 统计信息会显示在日志区域
- 如果启用了拆分功能，结果文件会保存到输出目录
- 如果启用了视频拷贝功能，错误视频会分类保存到目标目录

## 输出说明

### 统计信息
程序会输出每种场景的：
- 正样本总数、漏检数、正检数、召回率
- 负样本总数、误检数、精确率
- 详细的属性级统计分析

### 拆分文件
如果启用拆分功能，会按以下格式生成文件：
- `DMS_{报警掩码}_{场景名}_{样本类型}.txt`

### 视频分类
如果启用视频拷贝功能，会创建以下目录结构：
```
目标目录/
├── 漏检视频/
│   ├── DMS_call/
│   ├── DMS_smoke/
│   └── ...
└── 误检视频/
    ├── DMS_call/
    ├── DMS_smoke/
    └── ...
```

## 配置文件

程序会自动创建 `dms_config.ini` 配置文件来保存设置，包括：
- 路径配置
- 功能开关设置

## 注意事项

1. 确保源目录中包含视频文件和对应的标定文件（.txt格式）
2. 结果文件应为JSON Lines格式，包含alarmMask字段
3. 标定文件的第一行应包含完整的标定信息
4. 程序运行时会在日志区域显示详细的处理信息
5. 大文件处理可能需要较长时间，请耐心等待
6. DMS系统支持多种复杂场景，属性配置较为丰富

## 技术特性

- 基于tkinter的图形用户界面
- 多线程处理，避免界面冻结
- 实时日志显示和进度更新
- 配置文件自动保存/加载
- 灵活的参数配置
- 完整的错误处理和验证
- 支持11种DMS检测场景
- 丰富的属性维度分析

## 系统要求

- Python 3.6+
- tkinter（通常随Python安装）
- 标准库：json, os, shutil, threading, configparser, collections
