import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os

class SimpleToolLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("效果测试工具启动器")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 检查工具可用性
        self.check_tools()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="效果测试工具启动器", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明文字
        desc_label = ttk.Label(main_frame, text="请选择要启动的测试工具：", 
                              font=("Arial", 12))
        desc_label.pack(pady=(0, 30))
        
        # 工具列表框架
        tools_frame = ttk.LabelFrame(main_frame, text="可用工具", padding="15")
        tools_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # BSD工具
        bsd_frame = ttk.Frame(tools_frame)
        bsd_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(bsd_frame, text="BSD - 盲点检测系统", 
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        
        self.bsd_status_label = ttk.Label(bsd_frame, text="检查中...", 
                                         foreground="orange")
        self.bsd_status_label.pack(side=tk.RIGHT, padx=(0, 10))
        
        self.bsd_button = ttk.Button(bsd_frame, text="启动BSD", 
                                    command=self.launch_bsd, state="disabled")
        self.bsd_button.pack(side=tk.RIGHT)
        
        # 分隔线
        ttk.Separator(tools_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # DMS工具
        dms_frame = ttk.Frame(tools_frame)
        dms_frame.pack(fill=tk.X, pady=5)

        ttk.Label(dms_frame, text="DMS - 驾驶员监控系统",
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        self.dms_status_label = ttk.Label(dms_frame, text="检查中...",
                                         foreground="orange")
        self.dms_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.dms_button = ttk.Button(dms_frame, text="启动DMS",
                                    command=self.launch_dms, state="disabled")
        self.dms_button.pack(side=tk.RIGHT)

        # 分隔线
        ttk.Separator(tools_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # ADAS工具
        adas_frame = ttk.Frame(tools_frame)
        adas_frame.pack(fill=tk.X, pady=5)

        ttk.Label(adas_frame, text="ADAS - 高级驾驶辅助系统",
                 font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        self.adas_status_label = ttk.Label(adas_frame, text="检查中...",
                                          foreground="orange")
        self.adas_status_label.pack(side=tk.RIGHT, padx=(0, 10))

        self.adas_button = ttk.Button(adas_frame, text="启动ADAS",
                                     command=self.launch_adas, state="disabled")
        self.adas_button.pack(side=tk.RIGHT)


        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="刷新", command=self.check_tools).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="正在检查工具可用性...", 
                                     font=("Arial", 10), foreground="gray")
        self.status_label.pack(pady=(10, 0))
    
    def check_tools(self):
        """检查工具可用性"""
        available_count = 0
        total_count = 3

        # 检查BSD
        if os.path.exists("BSD_effect_test_UI.py"):
            self.bsd_status_label.config(text="✓ 可用", foreground="green")
            self.bsd_button.config(state="normal")
            available_count += 1
        else:
            self.bsd_status_label.config(text="✗ 不可用", foreground="red")
            self.bsd_button.config(state="disabled")

        # 检查DMS
        if os.path.exists("DMS_effect_test_UI.py"):
            self.dms_status_label.config(text="✓ 可用", foreground="green")
            self.dms_button.config(state="normal")
            available_count += 1
        else:
            self.dms_status_label.config(text="✗ 不可用", foreground="red")
            self.dms_button.config(state="disabled")

        # 检查ADAS
        if os.path.exists("ADAS_effect_test_UI.py"):
            self.adas_status_label.config(text="✓ 可用", foreground="green")
            self.adas_button.config(state="normal")
            available_count += 1
        else:
            self.adas_status_label.config(text="✗ 不可用", foreground="red")
            self.adas_button.config(state="disabled")



        # 更新状态
        if available_count == total_count:
            self.status_label.config(text=f"所有工具可用 ({available_count}/{total_count})", foreground="green")
        elif available_count > 0:
            self.status_label.config(text=f"部分工具可用 ({available_count}/{total_count})", foreground="orange")
        else:
            self.status_label.config(text="未找到可用工具", foreground="red")
    
    def launch_bsd(self):
        """启动BSD工具"""
        self.launch_tool("BSD_effect_test_UI.py", "BSD")
    
    def launch_dms(self):
        """启动DMS工具"""
        self.launch_tool("DMS_effect_test_UI.py", "DMS")

    def launch_adas(self):
        """启动ADAS工具"""
        self.launch_tool("ADAS_effect_test_UI.py", "ADAS")


    
    def launch_tool(self, script_file, tool_name):
        """启动指定工具"""
        script_path = os.path.abspath(script_file)
        
        if not os.path.exists(script_path):
            messagebox.showerror("错误", f"找不到文件: {script_file}")
            return
        
        try:
            self.status_label.config(text=f"正在启动 {tool_name}...", foreground="blue")
            self.root.update()

            # 启动工具
            subprocess.Popen([sys.executable, script_path])

            # 短暂延迟后显示启动成功
            self.root.after(1000, lambda: self.status_label.config(text=f"{tool_name} 已启动", foreground="green"))

        except Exception as e:
            error_msg = f"启动 {tool_name} 失败：\n{str(e)}"
            messagebox.showerror("启动失败", error_msg)
            self.status_label.config(text="启动失败", foreground="red")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """效果测试工具启动器 v2.1

这是一个简化版的工具启动器，用于启动各种效果测试工具。

当前支持的工具：
• BSD - 盲点检测系统效果测试
• DMS - 驾驶员监控系统效果测试
• ADAS - 高级驾驶辅助系统效果测试

使用方法：
1. 确保工具文件存在于同一目录
2. 点击对应的启动按钮
3. 工具将在新窗口中打开

特性：
• 简洁的界面设计
• 自动检测工具可用性
• 一键启动功能
• 跨平台支持
• 支持多种测试系统"""

        messagebox.showinfo("关于", about_text)


def main():
    """主程序入口"""
    root = tk.Tk()
    app = SimpleToolLauncher(root)
    root.mainloop()


if __name__ == "__main__":
    main()
