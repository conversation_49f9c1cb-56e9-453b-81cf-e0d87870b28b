import json
import os

"""
注册图提取情况
{
   错误码1: ["注册图1的名称", "注册图2的名称", .....],
   错误码2: ["注册图1的名称", "注册图2的名称", .....] 
   .....
}
"""
register_feature_dict = dict()
"""
注册图特征提取成功的id列表
"""
register_feature_success_id_list = list()

"""
识别图图提取失败情况
{
   错误码1: ["识别图1的名称", "识别图2的名称", .....],
   错误码2: ["识别图1的名称", "识别图2的名称", .....] 
   .....
}
"""
recognize_feature_failed_dict = dict()

recognize_feature_failed_register_list = list()    # 识别图特征提取失败，但此人脸未注册
recognize_feature_failed_unregister_list = list()  # 识别图特征提取失败，但此人脸未注册

recognize_register_count = 0    # 识别图中，人脸已注册数量总计
recognize_unregister_count = 0  # 识别图中，人脸未注册数量总计

"""
比对失败
{
   "错误码1": [("注册图1的名称", "识别图1的名称"), ("注册图2的名称", "识别图2的名称"), .....],
   "错误码2": [("注册图1的名称", "识别图1的名称"), ("注册图2的名称", "识别图2的名称"), .....] 
   .....
}
"""
pair_match_failed_dict = dict()

"""
比对成功，正识，即相似度大于预期值且为同一个人
{
   "注册图1": ["识别图1的名称", "识别图2的名称", .....],
   "注册图2": ["识别图1的名称", "识别图2的名称", .....] 
   .....
}
"""
pair_match_correct_dict = dict()

"""
比对成功，误识，相似度大于预期值但不是同一个人
{
   "注册图1": ["识别图1的名称", "识别图2的名称", .....],
   "注册图2": ["识别图1的名称", "识别图2的名称", .....] 
   .....
}
"""
pair_match_false_acceptance_dict = dict()

"""
比对成功，拒识，相似度小于预期值但为同一个人
{
   "注册图1": ["识别图1的名称", "识别图2的名称", .....],
   "注册图2": ["识别图1的名称", "识别图2的名称", .....] 
   .....
}
"""
pair_match_false_rejection_dict = dict()


# 打印统计后的结果
def print_count_result(is_print_detail=True):
    print("=========== register feature detail ===========")
    for error_code, register_pics in register_feature_dict.items():
        print("error_code: {}, count: {}".format(error_code, len(register_pics)))
        if is_print_detail:
            print("*** error_code[{}] ***".format(error_code))
            for register_pic in register_pics:
                print(register_pic)
    print("=========== recognize feature failed detail ===========")
    recognize_feature_failed_count = 0
    for error_code, recognize_pics in recognize_feature_failed_dict.items():
        recognize_feature_failed_count += len(recognize_pics)
        print("error_code: {}, count: {}".format(error_code, len(recognize_pics)))
        if is_print_detail:
            print("*** error_code[{}] ***".format(error_code))
            for recognize_pic in recognize_pics:
                print(recognize_pic)
    print("识别图特征提取失败总数: {}".format(recognize_feature_failed_count))
    print("识别图，已注册人脸，但特征提取失败总数: {}".format(len(recognize_feature_failed_register_list)))
    if is_print_detail:
        print("识别图，已注册人脸特征提取失败图片为：", " ".format(recognize_feature_failed_register_list))
    print("识别图，未注册人脸，但特征提取失败总数: {}".format(len(recognize_feature_failed_unregister_list)))
    if is_print_detail:
        print("识别图，未注册人脸特征提取失败图片为：", " ".format(recognize_feature_failed_unregister_list))

    print("=========== pair match failed detail ===========")
    for error_code, pair_match_failed_pics in pair_match_failed_dict.items():
        print("error_code: {}, count: {}".format(error_code, len(pair_match_failed_pics)))
        if is_print_detail:
            print("*** error_code[{}] ***(register_pic_name, recognize_pic_name)".format(error_code))
            for pair_match_failed_pic in pair_match_failed_pics:
                print(pair_match_failed_pic)
    print("=========== pair match correct detail, 即正识 ===========")
    pair_match_correct_count = 0
    for register_pic_name, recognize_pics in pair_match_correct_dict.items():
        pair_match_correct_count += len(recognize_pics)
        if is_print_detail:
            print("register_pic_name: {}, pair match correct count: {}".format(register_pic_name, len(recognize_pics)))
    print("应正识总数： {}， 实际正识总数： {}".format(recognize_register_count, pair_match_correct_count))

    print("=========== pair match false acceptance detail, 即误识 ===========")
    pair_match_false_acceptance_count = 0
    for register_pic_name, recognize_pics in pair_match_false_acceptance_dict.items():
        pair_match_false_acceptance_count += len(recognize_pics)
        if is_print_detail:
            print("register_pic_name: {}, pair match false acceptance count: {}, recognize_pics: {}".format(register_pic_name, len(recognize_pics), " ".join(recognize_pics)))
    print("与非本人比对总次数：{}， 与非本人比对通过总次数即误识总数： {}， 误识率： {:.4f}%".format(recognize_unregister_count, pair_match_false_acceptance_count, pair_match_false_acceptance_count * 100 / recognize_unregister_count))

    print("=========== pair match false rejection detail, 即拒识 ===========")
    pair_match_false_rejection_count = 0
    for register_pic_name, recognize_pics in pair_match_false_rejection_dict.items():
        pair_match_false_rejection_count += len(recognize_pics)
        if is_print_detail:
            print("register_pic_name: {}, pair match false rejection count: {}".format(register_pic_name, len(recognize_pics), recognize_pics))
    print("本人比对总次数： {}， 本人拒绝总次数即拒识总数： {}， 拒识率： {:.4f}%".format(recognize_register_count, pair_match_false_rejection_count, pair_match_false_rejection_count * 100 / recognize_register_count))


# 将测试结果进行统计，归档为特征提取失败、拒识、误识等
def result_count(result_file, expect_similarity_score, is_print_detail=True):
    with open(result_file, "r") as f:
        all_infos = f.readlines()
    for line in all_infos:
        line_json = json.loads(line)
        # 注册图特征提取结果归档
        if line_json.get("register_feature_status") is not None:
            register_feature_status = line_json.get("register_feature_status")
            register_pic_name = line_json.get("register_pic_name")
            if register_feature_dict.get(register_feature_status) is None:
                register_feature_dict[register_feature_status] = list()
            register_feature_dict[register_feature_status].append(register_pic_name)
            if register_feature_status == 0:
                register_feature_success_id_list.append(register_pic_name.split("_")[0])
            continue

        # 识别图特征提取与比对结果归档
        if line_json.get("recognize_feature_status") is not None:
            recognize_feature_status = line_json.get("recognize_feature_status")
            recognize_pic_name = line_json.get("recognize_pic_name")

            # 识别图特征提取失败
            if recognize_feature_status != 0:
                if recognize_feature_failed_dict.get(recognize_feature_status) is None:
                    recognize_feature_failed_dict[recognize_feature_status] = list()
                recognize_feature_failed_dict[recognize_feature_status].append(recognize_pic_name)
                if recognize_pic_name.split("_")[0] in register_feature_success_id_list:
                    recognize_feature_failed_register_list.append(recognize_pic_name)
                else:
                    recognize_feature_failed_unregister_list.append(recognize_pic_name)
                continue

            # 识别图特征提取成功，但比对失败
            pair_match_status = line_json.get("pair_match_status")
            register_pic_name = line_json.get("register_pic_name")
            if pair_match_status != 0:
                if pair_match_failed_dict.get(pair_match_status) is None:
                    pair_match_failed_dict[pair_match_status] = list()
                pair_match_failed_dict[pair_match_status].append((register_pic_name, recognize_pic_name))
                continue

            similarityScore = float(line_json.get("similarityScore"))

            register_id = register_pic_name.split("_")[0]
            recognize_id = recognize_pic_name.split("_")[0]
            if register_id == recognize_id:
                global recognize_register_count
                recognize_register_count += 1
            else:
                global recognize_unregister_count
                recognize_unregister_count += 1

            # 比对成功，正识，即相似度大于预期值且为同一个人
            if register_id == recognize_id and similarityScore >= expect_similarity_score:
                if pair_match_correct_dict.get(register_pic_name) is None:
                    pair_match_correct_dict[register_pic_name] = list()
                pair_match_correct_dict[register_pic_name].append(recognize_pic_name)

            # 比对成功，误识，相似度大于预期值但不是同一个人
            if register_id != recognize_id and similarityScore >= expect_similarity_score:
                if pair_match_false_acceptance_dict.get(register_pic_name) is None:
                    pair_match_false_acceptance_dict[register_pic_name] = list()
                pair_match_false_acceptance_dict[register_pic_name].append(recognize_pic_name)

            # 比对成功，拒识，相似度小于预期值但为同一个人
            if register_id == recognize_id and similarityScore < expect_similarity_score:
                if pair_match_false_rejection_dict.get(register_pic_name) is None:
                    pair_match_false_rejection_dict[register_pic_name] = list()
                pair_match_false_rejection_dict[register_pic_name].append(recognize_pic_name)
    print_count_result(is_print_detail)


# 获取文件夹下各文件的父目录名称
def get_files_parent_name(parent_path):
    files_parent_path_name_dict = dict()
    for root, dirs, files in os.walk(parent_path):
        for f in files:
            parent_path_name = root.split("\\")[-1]
            files_parent_path_name_dict[f] = parent_path_name
    return files_parent_path_name_dict


# 将拆分后的结果写入不同文件
def write_info_to_txt(result_path, result_dict, register_pic_result, recognize_pic_result):
    for filename, result in result_dict.items():
        register_pic_parent_path_name, recognize_pic_parent_path_name = filename.split("-")
        txt_file = os.path.join(result_path, "{}.txt".format(filename))
        if os.path.exists(txt_file):
            os.remove(txt_file)
        print("result file write...", filename)
        with open(txt_file, "a+", encoding="utf-8") as f:
            if register_pic_result.get(register_pic_parent_path_name) is not None:
                for line in register_pic_result.get(register_pic_parent_path_name):
                    f.write(line)
            if recognize_pic_result.get(recognize_pic_parent_path_name) is not None:
                for line in recognize_pic_result.get(recognize_pic_parent_path_name):
                    f.write(line)
            for line in result:
                f.write(line)


# 读取测试结果，并按照图片所在不同目录进行归档
def split_result(result_file, result_path, register_folder_name, recognize_folder_name):
    register_files_parent_path_name_dict = get_files_parent_name(register_folder_name)
    recognize_files_parent_path_name_dict = get_files_parent_name(recognize_folder_name)
    register_pic_result = dict()
    recognize_pic_result = dict()
    pair_match_result = dict()
    with open(result_file, "r") as f:
        all_infos = f.readlines()
    for line in all_infos:
        line_json = json.loads(line)

        if line_json.get("register_feature_status") is not None:
            register_pic_name = line_json.get("register_pic_name")
            register_pic_parent_path_name = register_files_parent_path_name_dict.get(register_pic_name)
            if register_pic_parent_path_name is None:
                print("[ERROR] file: {}, can't find parent path".format(register_pic_name))
                return
            if register_pic_result.get(register_pic_parent_path_name) is None:
                register_pic_result[register_pic_parent_path_name] = list()
            register_pic_result[register_pic_parent_path_name].append(line)
            continue

        recognize_feature_status = line_json.get("recognize_feature_status")
        if recognize_feature_status is not None:
            register_pic_name = line_json.get("register_pic_name")
            register_pic_parent_path_name = register_files_parent_path_name_dict.get(register_pic_name)
            recognize_pic_name = line_json.get("recognize_pic_name")
            recognize_pic_parent_path_name = recognize_files_parent_path_name_dict.get(recognize_pic_name)
            if recognize_pic_parent_path_name is None:
                print("[ERROR] file: {}, can't find parent path".format(recognize_pic_name))
                return
            if recognize_feature_status != 0:
                if not recognize_pic_result.get(recognize_pic_parent_path_name):
                    recognize_pic_result[recognize_pic_parent_path_name] = list()
                recognize_pic_result[recognize_pic_parent_path_name].append(line)
                continue
            pair_match_result_file_name = "{}-{}".format(register_pic_parent_path_name, recognize_pic_parent_path_name)
            if not pair_match_result.get(pair_match_result_file_name):
                pair_match_result[pair_match_result_file_name] = list()
            pair_match_result[pair_match_result_file_name].append(line)
    write_info_to_txt(result_path, pair_match_result, register_pic_result, recognize_pic_result)


def split_result_test():
    # result_file = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\23798瑞驰曼Hi3516cv610\D2-8[23798-2]\源文件\5_2.1.0031.2102.265_20250408_151150_result.txt"
    # result_path = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\23798瑞驰曼Hi3516cv610\D2-8[23798-2]\测试结果"
    # register_folder_name = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\FR\picture\register"
    # recognize_folder_name = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\FR\picture\recognize"

    ##对应的face_result
    result_file = r"G:\FaceID_RESULT_2.1.026.1403.105_20230325_003204\FaceID_info_2.1.026.1403.105_20230325_003204.txt"
    # "G:\FaceID_RESULT_2.1.026.1403.105_20230325_180707\FaceID_info_2.1.026.1403.105_20230325_180707.txt"  ir-ir
    # "G:\FaceID_RESULT_2.1.026.1403.105_20230325_185153\FaceID_info_2.1.026.1403.105_20230325_185153.txt"  ir-rgb
    # "G:\FaceID_RESULT_2.1.026.1403.105_20230325_190913\FaceID_info_2.1.026.1403.105_20230325_190913.txt"  rgb-ir
    # "G:\FaceID_RESULT_2.1.026.1403.105_20230325_193201\FaceID_info_2.1.026.1403.105_20230325_193201.txt"
    # "G:\FaceID_RESULT_2.1.026.1403.105_20230325_003204\FaceID_info_2.1.026.1403.105_20230325_003204.txt"  rgb-rgb
    ##拆分后的保存结果文件的路径
    result_path = r"G:\24121兆岳MT8666\测试结果"
    ##整机素材文件路径
    register_folder_name = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\FR\picture\register"
    recognize_folder_name = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\SDK_Standard\整机素材\FR\picture\recognize"
    split_result(result_file, result_path, register_folder_name, recognize_folder_name)


def result_count_test():
    # result_file = r"\\hz-iotfs\testlib_IOT\SmartCar\ADAS\Sample\ProjectSample\Commercial_Vehicle\后装\OEM\23798瑞驰曼Hi3516cv610\D2-8[23798-2]\测试结果\RGB_register-RGB_recognize.txt"
    ##拆分后的结果.txt文件路径
    result_file = r"G:\24121兆岳MT8666\测试结果\RGB_register-RGB_recognize.txt"
    # "G:\24121兆岳MT8666\测试结果\IR_register-IR_recognize.txt"
    # "G:\24121兆岳MT8666\测试结果\IR_register-RGB_recognize.txt"
    # "G:\24121兆岳MT8666\测试结果\RGB_register-IR_recognize.txt"
    # "G:\24121兆岳MT8666\测试结果\RGB_register-RGB_recognize.txt"
    expect_similarity_score = 0.75
    # is_print_detail = False
    is_print_detail = True
    result_count(result_file, expect_similarity_score, is_print_detail)


if __name__ == "__main__":
    # 将FR测试结果拆分，按照注册图、识别图的目录名称进行拆分
    # fr测试结果分析统计   先通过split_result_test方法将结果进行拆分得到对应的ir-rgb.txt文件   然后调用result_count_test对对应的txt文件进行分析得到结果
    # split_result_test()
    result_count_test()
