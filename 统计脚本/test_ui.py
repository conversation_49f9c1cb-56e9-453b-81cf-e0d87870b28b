#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BSD效果测试工具UI版本的简单测试脚本
"""

import tkinter as tk
from BSD_effect_test_UI import BSDEffectTestUI

def test_ui():
    """测试UI界面"""
    print("正在启动BSD效果测试工具UI...")
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用实例
        app = BSDEffectTestUI(root)
        
        print("UI界面已成功创建！")
        print("界面功能包括：")
        print("1. 路径配置区域 - 选择源目录、结果文件等")
        print("2. 功能配置区域 - 测试模式和功能开关")
        print("3. 高级配置区域 - 编辑算法映射、属性定义等")
        print("4. 控制按钮区域 - 开始处理、保存配置等")
        print("5. 日志显示区域 - 实时显示处理进度")
        print("6. 进度条 - 显示处理状态")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"启动UI时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui()
