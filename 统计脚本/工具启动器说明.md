# 工具启动器 - 简化版

## 🚀 快速开始

### 启动程序
```bash
python Tool_Launcher.py
```

## 📋 界面说明

### 主界面布局
```
┌─────────────────────────────────────┐
│        效果测试工具启动器            │
├─────────────────────────────────────┤
│     请选择要启动的测试工具：         │
│                                     │
│  ┌─── 可用工具 ──────────────────┐   │
│  │ BSD - 盲点检测系统    ✓可用 [启动BSD] │
│  │ ─────────────────────────────  │
│  │ DMS - 驾驶员监控系统  ✓可用 [启动DMS] │
│  │ ─────────────────────────────  │
│  │ ADAS - 高级驾驶辅助系统 ✓可用 [启动ADAS] │
│  └───────────────────────────────┘   │
│                                     │
│  [刷新] [关于]              [退出]   │
│                                     │
│  状态：所有工具可用 (3/3)            │
└─────────────────────────────────────┘
```

### 功能说明

#### 工具状态指示
- **✓ 可用** (绿色) - 工具文件存在，可以启动
- **✗ 不可用** (红色) - 找不到工具文件
- **检查中...** (橙色) - 正在检测工具状态

#### 按钮功能
- **启动BSD** - 启动盲点检测系统测试工具
- **启动DMS** - 启动驾驶员监控系统测试工具
- **启动ADAS** - 启动高级驾驶辅助系统测试工具
- **刷新** - 重新检测工具可用性
- **关于** - 显示启动器信息
- **退出** - 关闭启动器

#### 状态栏信息
- **所有工具可用 (3/3)** - 3个工具都可用
- **部分工具可用 (X/3)** - 部分工具可用
- **未找到可用工具** - 没有找到任何工具
- **正在启动 XXX...** - 正在启动指定工具
- **工具已启动** - 工具启动成功

## 🎯 支持的工具

### BSD（盲点检测系统）
- **文件**: `BSD_effect_test_UI.py`
- **功能**: 盲点检测算法效果测试
- **特性**: 支持多种摄像头配置和属性分析

### DMS（驾驶员监控系统）
- **文件**: `DMS_effect_test_UI.py`
- **功能**: 驾驶员监控算法效果测试
- **特性**: 支持11种检测场景和Excel报告

### ADAS（高级驾驶辅助系统）
- **文件**: `ADAS_effect_test_UI.py`
- **功能**: 高级驾驶辅助算法效果测试
- **特性**: 支持6种检测场景和可视化配置
- **分析**: LDW、FCW、HMW、PCW、PVS、斑马线检测

## 🔧 使用流程

### 1. 检查工具状态
启动器会自动检测工具文件是否存在：
- 如果文件存在，状态显示"✓ 可用"，按钮可点击
- 如果文件不存在，状态显示"✗ 不可用"，按钮禁用

### 2. 启动工具
点击对应的启动按钮：
- 程序会在新进程中启动对应的工具
- 状态栏显示启动成功信息
- 启动器保持运行，可以继续启动其他工具

## ❓ 常见问题

**Q: 工具显示"不可用"怎么办？**
A: 确保对应的工具文件存在于同一目录，然后点击"刷新"

**Q: 点击启动按钮没反应？**
A: 检查工具状态是否为"可用"，禁用状态的按钮无法点击

**Q: 启动失败怎么办？**
A: 确认Python环境正确，检查工具文件是否有语法错误

**Q: 如何添加新工具？**
A: 修改代码中的工具配置，添加新的检测和启动逻辑

## 🎨 设计特点

### 简洁设计
- **清晰布局**: 工具列表一目了然
- **状态明确**: 每个工具的状态清楚显示
- **操作简单**: 一键启动，无复杂配置

### 用户友好
- **自动检测**: 启动时自动检查工具可用性
- **实时反馈**: 启动过程有状态提示
- **灵活选择**: 可选择是否关闭启动器

### 技术特性
- **轻量级**: 代码简洁，启动快速
- **跨平台**: 支持Windows/macOS/Linux
- **易扩展**: 添加新工具只需几行代码

## 🔄 扩展方法

### 添加新工具
1. **在界面中添加工具框架**
2. **添加检测逻辑**
3. **添加启动函数**
4. **更新状态统计**

### 示例：添加LDW工具
```python
# 1. 添加界面元素
ldw_frame = ttk.Frame(tools_frame)
ldw_frame.pack(fill=tk.X, pady=5)

# 2. 添加检测逻辑
if os.path.exists("LDW_effect_test_UI.py"):
    self.ldw_status_label.config(text="✓ 可用", foreground="green")
    self.ldw_button.config(state="normal")

# 3. 添加启动函数
def launch_ldw(self):
    self.launch_tool("LDW_effect_test_UI.py", "LDW")
```

---

**版本**: v2.1 | **特性**: 支持BSD、DMS、ADAS三种测试工具
