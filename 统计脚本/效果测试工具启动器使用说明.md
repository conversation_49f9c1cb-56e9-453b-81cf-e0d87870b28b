# 效果测试工具启动器 - 使用说明

## 🚀 概述

效果测试工具启动器是一个通用的工具管理界面，用于统一管理和启动各种效果测试工具。通过这个启动器，您可以方便地选择和启动不同的测试工具，而无需记住各个工具的文件名或手动启动。

## ✨ 主要特性

### 🎯 **统一管理**
- **一个入口**：所有测试工具的统一启动入口
- **可视化界面**：直观的卡片式工具选择界面
- **状态检测**：自动检测工具文件是否存在
- **智能启动**：一键启动对应的测试工具

### 🔧 **功能特性**
- **工具卡片**：每个工具都有独立的信息卡片
- **状态指示**：实时显示工具可用状态
- **自动检测**：启动时自动检查工具文件
- **跨平台支持**：Windows/macOS/Linux通用

## 🎮 **使用方法**

### 1. 启动程序
```bash
python Effect_Test_Launcher.py
```
或双击 `Effect_Test_Launcher.py` 文件

### 2. 界面说明
启动器界面包含以下区域：

#### 工具选择区域
- **BSD卡片**：盲点检测系统工具
  - 绿色图标，显示工具状态
  - 点击"启动 BSD"按钮启动工具
  
- **DMS卡片**：驾驶员监控系统工具
  - 蓝色图标，显示工具状态
  - 点击"启动 DMS"按钮启动工具

- **预留位置**：为未来新工具预留的位置
  - 灰色占位卡片
  - 显示"即将推出"

#### 状态信息区域
- **左侧状态**：显示工具检测结果
- **右侧版本**：显示启动器版本信息

#### 控制按钮区域
- **刷新**：重新检测工具可用性
- **关于**：显示启动器详细信息
- **退出**：关闭启动器

### 3. 启动工具
1. **选择工具**：点击对应工具卡片上的启动按钮
2. **确认启动**：工具将在新窗口中打开
3. **选择操作**：可以选择是否关闭启动器

## 📊 **支持的工具**

### 🎯 **BSD（盲点检测系统）**
- **文件名**：`BSD_effect_test_UI.py`
- **功能**：盲点检测算法效果测试
- **特性**：支持俯视、平行、鱼眼摄像头配置
- **分析**：行人状态、车辆状态、高度信息

### 🚗 **DMS（驾驶员监控系统）**
- **文件名**：`DMS_effect_test_UI.py`
- **功能**：驾驶员监控算法效果测试
- **特性**：支持11种检测场景
- **分析**：打电话、抽烟、闭眼、打哈欠等

## 🔧 **工具状态说明**

### 状态指示器
- **✓ 可用**：工具文件存在，可以正常启动
- **✗ 文件不存在**：找不到对应的工具文件
- **检查中...**：正在检测工具状态

### 总体状态
- **所有工具可用 (2/2)**：所有工具都可以使用
- **部分工具可用 (1/2)**：部分工具可以使用
- **未找到可用工具**：没有找到任何可用工具

## 🚀 **扩展新工具**

### 添加新工具的步骤

#### 1. 修改工具注册表
在 `ToolRegistry` 类中添加新工具：
```python
"NEW_TOOL": {
    "title": "新工具名称",
    "description": "新工具的详细描述\n支持的功能特性",
    "script": "new_tool_UI.py",
    "color": "#FF9800"  # 工具图标颜色
}
```

#### 2. 修改界面布局
在 `create_widgets` 方法中添加新工具卡片：
```python
self.create_tool_card(tools_frame, row, col, "NEW_TOOL", "新工具名称",
                     "新工具描述", "new_tool_UI.py", "#FF9800")
```

#### 3. 更新检测逻辑
在 `check_tools_availability` 方法中添加新工具的检测：
```python
# 检查新工具
new_tool_available = os.path.exists("new_tool_UI.py")
if new_tool_available:
    self.new_tool_status.set("✓ 可用")
    # ... 其他逻辑
```

### 扩展示例
假设要添加一个"车道线检测"工具：

1. **工具信息**：
   - 名称：LDW（Lane Departure Warning）
   - 文件：`LDW_effect_test_UI.py`
   - 颜色：橙色 `#FF9800`

2. **添加步骤**：
   - 在工具注册表中添加LDW配置
   - 在界面中添加LDW卡片
   - 在检测逻辑中添加LDW检测

## 💡 **使用技巧**

### 文件组织
建议将所有工具文件放在同一目录下：
```
工具目录/
├── Effect_Test_Launcher.py        # 启动器主程序
├── BSD_effect_test_UI.py          # BSD工具
├── DMS_effect_test_UI.py          # DMS工具
├── 效果测试工具启动器使用说明.md   # 使用说明
└── 其他工具文件...
```

### 快速启动
1. **桌面快捷方式**：为启动器创建桌面快捷方式
2. **批处理文件**：创建.bat文件快速启动
3. **环境变量**：将工具目录添加到PATH

### 故障排除
1. **工具显示不可用**：
   - 检查工具文件是否存在
   - 确认文件名是否正确
   - 点击"刷新"重新检测

2. **启动失败**：
   - 确认Python环境正确
   - 检查工具文件是否有语法错误
   - 查看错误提示信息

## 🎯 **适用场景**

- **算法开发团队**：需要频繁切换不同测试工具
- **测试工程师**：需要统一的工具管理界面
- **研究人员**：需要对比不同算法的测试工具
- **项目管理**：需要为团队提供统一的工具入口

## 📝 **技术特性**

- **基于tkinter**：跨平台图形界面
- **多线程检测**：后台检测工具状态
- **进程管理**：独立进程启动工具
- **模块化设计**：易于扩展和维护
- **错误处理**：完整的异常处理机制

## 🔄 **版本更新**

### v1.0 特性
- 支持BSD和DMS工具
- 自动状态检测
- 统一启动界面
- 扩展性设计

### 未来计划
- 支持更多测试工具
- 工具配置管理
- 批量操作功能
- 工具使用统计

---

**版本**: v1.0 | **特性**: 通用工具启动器，支持BSD和DMS，易于扩展
